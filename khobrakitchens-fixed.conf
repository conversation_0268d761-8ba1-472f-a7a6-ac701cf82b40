# Nginx Configuration for Expert Wonders (عجائب الخبراء)
# Domain: khobrakitchens.com
# Server IP: ************

# إعادة توجيه www إلى non-www
server {
    listen 80;
    server_name www.khobrakitchens.com;
    return 301 http://khobrakitchens.com$request_uri;
}

# إعادة التوجيه من HTTP إلى HTTPS
server {
    listen 80;
    server_name khobrakitchens.com;
    return 301 https://$host$request_uri;
}

# إعادة توجيه www إلى non-www (HTTPS)
server {
    listen 443 ssl;
    server_name www.khobrakitchens.com;

    ssl_certificate /etc/letsencrypt/live/khobrakitchens.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/khobrakitchens.com/privkey.pem;

    return 301 https://khobrakitchens.com$request_uri;
}

# السيرفر الرئيسي عبر HTTPS
server {
    listen 443 ssl;
    server_name khobrakitchens.com;

    root /var/www/html/dist;
    index index.html index.htm index.php;

    ssl_certificate /etc/letsencrypt/live/khobrakitchens.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/khobrakitchens.com/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # ملفات غير مسموح الوصول إليها (حماية مباشرة)
    location ~* \.(env|db)$ {
        deny all;
        return 403;
    }

    # حماية مجلدات حساسة
    location ^~ /admin/ {
        # deny all;
        # return 403;
        try_files $uri $uri/ /index.html;
    }

    location ^~ /database/ {
        deny all;
        return 403;
    }

    # دعم SPA أو تطبيق React.js
    location / {
        try_files $uri /index.html;
    }

    # دعم ملفات السايت ماب والروبوتس
    location = /sitemap.xml {
        allow all;
    }

    location = /robots.txt {
        allow all;
    }

    # API endpoints - proxy to Node.js API server
    location /api/ {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # دعم PHP إذا كنت تستخدمه
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;  # عدل حسب إصدار PHP
    }

    # ملفات ثابتة
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|otf|json)$ {
        expires 30d;
        access_log off;
        add_header Cache-Control "public";
    }

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;

    # Logging
    access_log /var/log/nginx/khobrakitchens.com.access.log;
    error_log /var/log/nginx/khobrakitchens.com.error.log;
}
