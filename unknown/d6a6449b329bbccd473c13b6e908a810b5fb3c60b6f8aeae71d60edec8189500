import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { HelmetProvider } from 'react-helmet-async'
import HomePage from './pages/HomePage'
import AdminPage from './pages/AdminPage'
import KitchensPage from './pages/KitchensPage'
import CabinetsPage from './pages/CabinetsPage'
import SEO from './components/SEO'

function App() {
  return (
    <HelmetProvider>
      <Router>
        <SEO />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/kitchens" element={<KitchensPage />} />
          <Route path="/cabinets" element={<CabinetsPage />} />
          <Route path="/admin" element={<AdminPage />} />
          <Route path="/admin/*" element={<AdminPage />} />
        </Routes>
      </Router>
    </HelmetProvider>
  )
}

export default App
