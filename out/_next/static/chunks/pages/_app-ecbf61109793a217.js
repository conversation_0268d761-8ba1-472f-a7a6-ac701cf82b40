(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{2149:(t,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>c});var a=r(7876);r(7790);var e=r(4232),n=r(5506);function c(t){let{Component:o,pageProps:r}=t,[c,i]=(0,e.useState)(!1);return((0,e.useEffect)(()=>{i(!0),(0,n.wU)().catch(console.error)},[]),c)?(0,a.jsx)("div",{dir:"rtl",children:(0,a.jsx)(o,{...r})}):null}},5506:(t,o,r)=>{"use strict";r.d(o,{aB:()=>n,wU:()=>e});var a=r(6686);let e=async()=>{try{return console.log("\uD83D\uDD04 Initializing database connection..."),await Promise.all([(0,a.zm)(),(0,a.ub)(),(0,a.KT)(),(0,a.WH)(),(0,a.wf)(),(0,a.bW)(),(0,a.t1)()]),console.log("✅ Database connection initialized successfully"),!0}catch(t){return console.error("❌ Failed to initialize database connection:",t),console.error("تأكد من أن API server يعمل على http://localhost:3002"),!1}},n=async()=>{try{return console.log("\uD83D\uDD04 Force resetting database..."),await e(),console.log("✅ Database connection reset completed"),!0}catch(t){return console.error("❌ Failed to force reset database:",t),!1}}},6556:(t,o,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return r(2149)}])},6686:(t,o,r)=>{"use strict";r.d(o,{$L:()=>P,Cm:()=>l,It:()=>h,KT:()=>s,OX:()=>f,WH:()=>u,aU:()=>i,bW:()=>b,dt:()=>p,ro:()=>v,sB:()=>d,t1:()=>T,ub:()=>g,wf:()=>m,y9:()=>w,yI:()=>y,zm:()=>c});let a=r(9478).i3.baseURL,e=async function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r=await fetch("".concat(a).concat(t),{headers:{"Content-Type":"application/json",...o.headers},...o});if(!r.ok)throw Error("HTTP error! status: ".concat(r.status));return await r.json()}catch(o){throw console.error("API call failed for ".concat(t,":"),o),o}},n=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:500;return new Promise(o=>setTimeout(o,t))},c=async()=>{try{return await n(),await e("/hero")}catch(t){throw console.error("خطأ في جلب بيانات البانر الرئيسي:",t),t}},i=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{await n();let o=await e("/hero"),r=(null==o?void 0:o.id)||1;return await e("/hero/".concat(r),{method:"PUT",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في تحديث بيانات البانر الرئيسي:",t),t}},s=async()=>{try{return await n(),await e("/kitchens")}catch(t){throw console.error("خطأ في جلب بيانات المطابخ:",t),t}},h=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/kitchens",{method:"POST",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في إضافة مطبخ:",t),t}},l=async function(t,o){arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{return await n(),await e("/kitchens/".concat(t),{method:"PUT",body:JSON.stringify(o)})}catch(t){throw console.error("خطأ في تحديث مطبخ:",t),t}},w=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/kitchens/".concat(t),{method:"DELETE"})}catch(t){throw console.error("خطأ في حذف مطبخ:",t),t}},u=async()=>{try{return await n(),await e("/cabinets")}catch(t){throw console.error("خطأ في جلب بيانات الخزائن:",t),t}},y=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/cabinets",{method:"POST",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في إضافة خزانة:",t),t}},d=async function(t,o){arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{return await n(),await e("/cabinets/".concat(t),{method:"PUT",body:JSON.stringify(o)})}catch(t){throw console.error("خطأ في تحديث خزانة:",t),t}},f=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/cabinets/".concat(t),{method:"DELETE"})}catch(t){throw console.error("خطأ في حذف خزانة:",t),t}},b=async()=>{try{return await n(),await e("/categories")}catch(t){throw console.error("خطأ في جلب الفئات:",t),t}},g=async()=>{try{return await n(),await e("/why-choose-us")}catch(t){throw console.error("خطأ في جلب بيانات لماذا تختارنا:",t),t}},v=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/why-choose-us",{method:"PUT",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في تحديث بيانات لماذا تختارنا:",t),t}},m=async()=>{try{return await n(),await e("/footer")}catch(t){throw console.error("خطأ في جلب بيانات التذييل:",t),t}},p=async function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return await n(),await e("/footer",{method:"PUT",body:JSON.stringify(t)})}catch(t){throw console.error("خطأ في تحديث بيانات التذييل:",t),t}},T=async()=>{try{return await n(),await e("/company-settings")}catch(t){throw console.error("خطأ في جلب إعدادات الشركة:",t),t}},P=async function(t,o){arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{return await n(),{success:!0}}catch(t){throw console.error("خطأ في تحديث إعدادات الشركة:",t),t}}},7790:()=>{},9478:(t,o,r)=>{"use strict";r.d(o,{bv:()=>e,i3:()=>a});let a="localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname?{baseURL:"http://localhost:3002",uploadsURL:""}:{baseURL:"".concat(window.location.protocol,"//").concat(window.location.host,"/api"),uploadsURL:""},e=t=>t?(t.startsWith("http://")||t.startsWith("https://"),t):null}},t=>{var o=o=>t(t.s=o);t.O(0,[593,792],()=>(o(6556),o(8253))),_N_E=t.O()}]);