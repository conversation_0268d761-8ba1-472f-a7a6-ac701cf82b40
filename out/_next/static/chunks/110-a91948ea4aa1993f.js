(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[110],{373:(e,t,i)=>{"use strict";function s(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let s=document;t&&(s=t.current);let r=i?.[e]??s.querySelectorAll(e);return r?Array.from(r):[]}return Array.from(e)}i.d(t,{K:()=>s})},508:(e,t,i)=>{"use strict";i.d(t,{W:()=>l});var s=i(4232),r=i(373);let a={some:0,all:1};function l(e,{root:t,margin:i,amount:n,once:o=!1,initial:d=!1}={}){let[p,u]=(0,s.useState)(d);return(0,s.useEffect)(()=>{if(!e.current||o&&p)return;let s={root:t&&t.current||void 0,margin:i,amount:n};return function(e,t,{root:i,margin:s,amount:l="some"}={}){let n=(0,r.K)(e),o=new WeakMap,d=new IntersectionObserver(e=>{e.forEach(e=>{let i=o.get(e.target);if(!!i!==e.isIntersecting)if(e.isIntersecting){let i=t(e.target,e);"function"==typeof i?o.set(e.target,i):d.unobserve(e.target)}else"function"==typeof i&&(i(e),o.delete(e.target))})},{root:i,rootMargin:s,threshold:"number"==typeof l?l:a[l]});return n.forEach(e=>d.observe(e)),()=>d.disconnect()}(e.current,()=>(u(!0),o?void 0:()=>u(!1)),s)},[t,e,i,o,n]),p}},1026:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let s=i(4232);function r(e,t){let i=(0,s.useRef)(null),r=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=i.current;e&&(i.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(i.current=a(e,s)),t&&(r.current=a(t,s))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1639:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return E},useLinkStatus:function(){return S}});let s=i(8365),r=i(7876),a=s._(i(4232)),l=i(6658),n=i(1851),o=i(6225),d=i(8407),p=i(2696),u=i(8265),c=i(2343),h=i(8940),f=i(7469),m=i(1026);i(3724);let g=new Set;function v(e,t,i,s){if((0,n.isLocalURL)(t)){if(!s.bypassPrefetchedCheck){let r=t+"%"+i+"%"+(void 0!==s.locale?s.locale:"locale"in e?e.locale:void 0);if(g.has(r))return;g.add(r)}e.prefetch(t,i,s).catch(e=>{})}}function w(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}let b=a.default.forwardRef(function(e,t){let i,s,{href:o,as:g,children:b,prefetch:y=null,passHref:S,replace:E,shallow:T,scroll:x,locale:C,onClick:M,onNavigate:P,onMouseEnter:L,onTouchStart:k,legacyBehavior:O=!1,..._}=e;i=b,O&&("string"==typeof i||"number"==typeof i)&&(i=(0,r.jsx)("a",{children:i}));let I=a.default.useContext(u.RouterContext),A=!1!==y,{href:z,as:$}=a.default.useMemo(()=>{if(!I){let e=w(o);return{href:e,as:g?w(g):e}}let[e,t]=(0,l.resolveHref)(I,o,!0);return{href:e,as:g?(0,l.resolveHref)(I,g):t||e}},[I,o,g]),D=a.default.useRef(z),j=a.default.useRef($);O&&(s=a.default.Children.only(i));let G=O?s&&"object"==typeof s&&s.ref:t,[N,B,F]=(0,c.useIntersection)({rootMargin:"200px"}),V=a.default.useCallback(e=>{(j.current!==$||D.current!==z)&&(F(),j.current=$,D.current=z),N(e)},[$,z,F,N]),R=(0,m.useMergedRef)(V,G);a.default.useEffect(()=>{I&&B&&A&&v(I,z,$,{locale:C})},[$,z,B,C,A,null==I?void 0:I.locale,I]);let q={ref:R,onClick(e){O||"function"!=typeof M||M(e),O&&s.props&&"function"==typeof s.props.onClick&&s.props.onClick(e),I&&(e.defaultPrevented||function(e,t,i,s,r,a,l,o,d){let{nodeName:p}=e.currentTarget;if(!("A"===p.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,n.isLocalURL)(i)){r&&(e.preventDefault(),location.replace(i));return}e.preventDefault(),(()=>{if(d){let e=!1;if(d({preventDefault:()=>{e=!0}}),e)return}let e=null==l||l;"beforePopState"in t?t[r?"replace":"push"](i,s,{shallow:a,locale:o,scroll:e}):t[r?"replace":"push"](s||i,{scroll:e})})()}}(e,I,z,$,E,T,x,C,P))},onMouseEnter(e){O||"function"!=typeof L||L(e),O&&s.props&&"function"==typeof s.props.onMouseEnter&&s.props.onMouseEnter(e),I&&v(I,z,$,{locale:C,priority:!0,bypassPrefetchedCheck:!0})},onTouchStart:function(e){O||"function"!=typeof k||k(e),O&&s.props&&"function"==typeof s.props.onTouchStart&&s.props.onTouchStart(e),I&&v(I,z,$,{locale:C,priority:!0,bypassPrefetchedCheck:!0})}};if((0,d.isAbsoluteUrl)($))q.href=$;else if(!O||S||"a"===s.type&&!("href"in s.props)){let e=void 0!==C?C:null==I?void 0:I.locale;q.href=(null==I?void 0:I.isLocaleDomain)&&(0,h.getDomainLocale)($,e,null==I?void 0:I.locales,null==I?void 0:I.domainLocales)||(0,f.addBasePath)((0,p.addLocale)($,e,null==I?void 0:I.defaultLocale))}return O?a.default.cloneElement(s,q):(0,r.jsx)("a",{..._,...q,children:i})}),y=(0,a.createContext)({pending:!1}),S=()=>(0,a.useContext)(y),E=b;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2161:()=>{},2343:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return o}});let s=i(4232),r=i(4754),a="function"==typeof IntersectionObserver,l=new Map,n=[];function o(e){let{rootRef:t,rootMargin:i,disabled:o}=e,d=o||!a,[p,u]=(0,s.useState)(!1),c=(0,s.useRef)(null),h=(0,s.useCallback)(e=>{c.current=e},[]);return(0,s.useEffect)(()=>{if(a){if(d||p)return;let e=c.current;if(e&&e.tagName)return function(e,t,i){let{id:s,observer:r,elements:a}=function(e){let t,i={root:e.root||null,margin:e.rootMargin||""},s=n.find(e=>e.root===i.root&&e.margin===i.margin);if(s&&(t=l.get(s)))return t;let r=new Map;return t={id:i,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=r.get(e.target),i=e.isIntersecting||e.intersectionRatio>0;t&&i&&t(i)})},e),elements:r},n.push(i),l.set(i,t),t}(i);return a.set(e,t),r.observe(e),function(){if(a.delete(e),r.unobserve(e),0===a.size){r.disconnect(),l.delete(s);let e=n.findIndex(e=>e.root===s.root&&e.margin===s.margin);e>-1&&n.splice(e,1)}}}(e,e=>e&&u(e),{root:null==t?void 0:t.current,rootMargin:i})}else if(!p){let e=(0,r.requestIdleCallback)(()=>u(!0));return()=>(0,r.cancelIdleCallback)(e)}},[d,i,t,p,c.current]),[h,p,(0,s.useCallback)(()=>{u(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2370:(e,t,i)=>{"use strict";function s(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function r(e,t){void 0===e&&(e={}),void 0===t&&(t={});let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:s(t[i])&&s(e[i])&&Object.keys(t[i]).length>0&&r(e[i],t[i])})}i.d(t,{a:()=>o,g:()=>l});let a={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function l(){let e="undefined"!=typeof document?document:{};return r(e,a),e}let n={document:a,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function o(){let e="undefined"!=typeof window?window:{};return r(e,n),e}},3724:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return i}});let i=e=>{}},3748:(e,t,i)=>{"use strict";i.d(t,{Ij:()=>d,t9:()=>c,Vx:()=>l,dK:()=>o,WO:()=>p});var s=i(2370),r=i(4569);function a(e,t,i,s){return e.params.createElements&&Object.keys(s).forEach(a=>{if(!i[a]&&!0===i.auto){let l=(0,r.e)(e.el,`.${s[a]}`)[0];l||((l=(0,r.c)("div",s[a])).className=s[a],e.el.append(l)),i[a]=l,t[a]=l}}),i}function l(e){let{swiper:t,extendParams:i,on:s,emit:l}=e;function n(e){let i;return e&&"string"==typeof e&&t.isElement&&(i=t.el.querySelector(e)||t.hostEl.querySelector(e))?i:(e&&("string"==typeof e&&(i=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&i&&i.length>1&&1===t.el.querySelectorAll(e).length?i=t.el.querySelector(e):i&&1===i.length&&(i=i[0])),e&&!i)?e:i}function o(e,i){let s=t.params.navigation;(e=(0,r.m)(e)).forEach(e=>{e&&(e.classList[i?"add":"remove"](...s.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=i),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](s.lockClass))})}function d(){let{nextEl:e,prevEl:i}=t.navigation;if(t.params.loop){o(i,!1),o(e,!1);return}o(i,t.isBeginning&&!t.params.rewind),o(e,t.isEnd&&!t.params.rewind)}function p(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),l("navigationPrev"))}function u(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),l("navigationNext"))}function c(){let e=t.params.navigation;if(t.params.navigation=a(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(e.nextEl||e.prevEl))return;let i=n(e.nextEl),s=n(e.prevEl);Object.assign(t.navigation,{nextEl:i,prevEl:s}),i=(0,r.m)(i),s=(0,r.m)(s);let l=(i,s)=>{i&&i.addEventListener("click","next"===s?u:p),!t.enabled&&i&&i.classList.add(...e.lockClass.split(" "))};i.forEach(e=>l(e,"next")),s.forEach(e=>l(e,"prev"))}function h(){let{nextEl:e,prevEl:i}=t.navigation;e=(0,r.m)(e),i=(0,r.m)(i);let s=(e,i)=>{e.removeEventListener("click","next"===i?u:p),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>s(e,"next")),i.forEach(e=>s(e,"prev"))}i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},s("init",()=>{!1===t.params.navigation.enabled?f():(c(),d())}),s("toEdge fromEdge lock unlock",()=>{d()}),s("destroy",()=>{h()}),s("enable disable",()=>{let{nextEl:e,prevEl:i}=t.navigation;if(e=(0,r.m)(e),i=(0,r.m)(i),t.enabled)return void d();[...e,...i].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),s("click",(e,i)=>{let{nextEl:s,prevEl:a}=t.navigation;s=(0,r.m)(s),a=(0,r.m)(a);let n=i.target,o=a.includes(n)||s.includes(n);if(t.isElement&&!o){let e=i.path||i.composedPath&&i.composedPath();e&&(o=e.find(e=>s.includes(e)||a.includes(e)))}if(t.params.navigation.hideOnClick&&!o){let e;if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===n||t.pagination.el.contains(n)))return;s.length?e=s[0].classList.contains(t.params.navigation.hiddenClass):a.length&&(e=a[0].classList.contains(t.params.navigation.hiddenClass)),!0===e?l("navigationShow"):l("navigationHide"),[...s,...a].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});let f=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),h()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),c(),d()},disable:f,update:d,init:c,destroy:h})}function n(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function o(e){let t,{swiper:i,extendParams:s,on:l,emit:o}=e,d="swiper-pagination";s({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${d}-bullet`,bulletActiveClass:`${d}-bullet-active`,modifierClass:`${d}-`,currentClass:`${d}-current`,totalClass:`${d}-total`,hiddenClass:`${d}-hidden`,progressbarFillClass:`${d}-progressbar-fill`,progressbarOppositeClass:`${d}-progressbar-opposite`,clickableClass:`${d}-clickable`,lockClass:`${d}-lock`,horizontalClass:`${d}-horizontal`,verticalClass:`${d}-vertical`,paginationDisabledClass:`${d}-disabled`}}),i.pagination={el:null,bullets:[]};let p=0;function u(){return!i.params.pagination.el||!i.pagination.el||Array.isArray(i.pagination.el)&&0===i.pagination.el.length}function c(e,t){let{bulletActiveClass:s}=i.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${s}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${s}-${t}-${t}`))}function h(e){let t=e.target.closest(n(i.params.pagination.bulletClass));if(!t)return;e.preventDefault();let s=(0,r.i)(t)*i.params.slidesPerGroup;if(i.params.loop){var a,l,o;if(i.realIndex===s)return;let e=(a=i.realIndex,l=s,(a%=o=i.slides.length,(l%=o)===a+1)?"next":l===a-1?"previous":void 0);"next"===e?i.slideNext():"previous"===e?i.slidePrev():i.slideToLoop(s)}else i.slideTo(s)}function f(){let e,s,a=i.rtl,l=i.params.pagination;if(u())return;let d=i.pagination.el;d=(0,r.m)(d);let h=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,f=i.params.loop?Math.ceil(h/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?(s=i.previousRealIndex||0,e=i.params.slidesPerGroup>1?Math.floor(i.realIndex/i.params.slidesPerGroup):i.realIndex):void 0!==i.snapIndex?(e=i.snapIndex,s=i.previousSnapIndex):(s=i.previousIndex||0,e=i.activeIndex||0),"bullets"===l.type&&i.pagination.bullets&&i.pagination.bullets.length>0){let n,o,u,h=i.pagination.bullets;if(l.dynamicBullets&&(t=(0,r.h)(h[0],i.isHorizontal()?"width":"height",!0),d.forEach(e=>{e.style[i.isHorizontal()?"width":"height"]=`${t*(l.dynamicMainBullets+4)}px`}),l.dynamicMainBullets>1&&void 0!==s&&((p+=e-(s||0))>l.dynamicMainBullets-1?p=l.dynamicMainBullets-1:p<0&&(p=0)),u=((o=(n=Math.max(e-p,0))+(Math.min(h.length,l.dynamicMainBullets)-1))+n)/2),h.forEach(e=>{let t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${l.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),d.length>1)h.forEach(t=>{let s=(0,r.i)(t);s===e?t.classList.add(...l.bulletActiveClass.split(" ")):i.isElement&&t.setAttribute("part","bullet"),l.dynamicBullets&&(s>=n&&s<=o&&t.classList.add(...`${l.bulletActiveClass}-main`.split(" ")),s===n&&c(t,"prev"),s===o&&c(t,"next"))});else{let t=h[e];if(t&&t.classList.add(...l.bulletActiveClass.split(" ")),i.isElement&&h.forEach((t,i)=>{t.setAttribute("part",i===e?"bullet-active":"bullet")}),l.dynamicBullets){let e=h[n],t=h[o];for(let e=n;e<=o;e+=1)h[e]&&h[e].classList.add(...`${l.bulletActiveClass}-main`.split(" "));c(e,"prev"),c(t,"next")}}if(l.dynamicBullets){let e=Math.min(h.length,l.dynamicMainBullets+4),s=(t*e-t)/2-u*t,r=a?"right":"left";h.forEach(e=>{e.style[i.isHorizontal()?r:"top"]=`${s}px`})}}d.forEach((t,s)=>{if("fraction"===l.type&&(t.querySelectorAll(n(l.currentClass)).forEach(t=>{t.textContent=l.formatFractionCurrent(e+1)}),t.querySelectorAll(n(l.totalClass)).forEach(e=>{e.textContent=l.formatFractionTotal(f)})),"progressbar"===l.type){let s;s=l.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";let r=(e+1)/f,a=1,o=1;"horizontal"===s?a=r:o=r,t.querySelectorAll(n(l.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${a}) scaleY(${o})`,e.style.transitionDuration=`${i.params.speed}ms`})}"custom"===l.type&&l.renderCustom?((0,r.s)(t,l.renderCustom(i,e+1,f)),0===s&&o("paginationRender",t)):(0===s&&o("paginationRender",t),o("paginationUpdate",t)),i.params.watchOverflow&&i.enabled&&t.classList[i.isLocked?"add":"remove"](l.lockClass)})}function m(){let e=i.params.pagination;if(u())return;let t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.grid&&i.params.grid.rows>1?i.slides.length/Math.ceil(i.params.grid.rows):i.slides.length,s=i.pagination.el;s=(0,r.m)(s);let a="";if("bullets"===e.type){let s=i.params.loop?Math.ceil(t/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&s>t&&(s=t);for(let t=0;t<s;t+=1)e.renderBullet?a+=e.renderBullet.call(i,t,e.bulletClass):a+=`<${e.bulletElement} ${i.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(a=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(a=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),i.pagination.bullets=[],s.forEach(t=>{"custom"!==e.type&&(0,r.s)(t,a||""),"bullets"===e.type&&i.pagination.bullets.push(...t.querySelectorAll(n(e.bulletClass)))}),"custom"!==e.type&&o("paginationRender",s[0])}function g(){let e;i.params.pagination=a(i,i.originalParams.pagination,i.params.pagination,{el:"swiper-pagination"});let t=i.params.pagination;t.el&&("string"==typeof t.el&&i.isElement&&(e=i.el.querySelector(t.el)),e||"string"!=typeof t.el||(e=[...document.querySelectorAll(t.el)]),e||(e=t.el),e&&0!==e.length&&(i.params.uniqueNavElements&&"string"==typeof t.el&&Array.isArray(e)&&e.length>1&&(e=[...i.el.querySelectorAll(t.el)]).length>1&&(e=e.find(e=>(0,r.b)(e,".swiper")[0]===i.el)),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(i.pagination,{el:e}),(e=(0,r.m)(e)).forEach(e=>{"bullets"===t.type&&t.clickable&&e.classList.add(...(t.clickableClass||"").split(" ")),e.classList.add(t.modifierClass+t.type),e.classList.add(i.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.classList.add(`${t.modifierClass}${t.type}-dynamic`),p=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&e.classList.add(t.progressbarOppositeClass),t.clickable&&e.addEventListener("click",h),i.enabled||e.classList.add(t.lockClass)})))}function v(){let e=i.params.pagination;if(u())return;let t=i.pagination.el;t&&(t=(0,r.m)(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(i.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",h))}),i.pagination.bullets&&i.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}l("changeDirection",()=>{if(!i.pagination||!i.pagination.el)return;let e=i.params.pagination,{el:t}=i.pagination;(t=(0,r.m)(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass)})}),l("init",()=>{!1===i.params.pagination.enabled?w():(g(),m(),f())}),l("activeIndexChange",()=>{void 0===i.snapIndex&&f()}),l("snapIndexChange",()=>{f()}),l("snapGridLengthChange",()=>{m(),f()}),l("destroy",()=>{v()}),l("enable disable",()=>{let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList[i.enabled?"remove":"add"](i.params.pagination.lockClass))}),l("lock unlock",()=>{f()}),l("click",(e,t)=>{let s=t.target,a=(0,r.m)(i.pagination.el);if(i.params.pagination.el&&i.params.pagination.hideOnClick&&a&&a.length>0&&!s.classList.contains(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&s===i.navigation.nextEl||i.navigation.prevEl&&s===i.navigation.prevEl))return;!0===a[0].classList.contains(i.params.pagination.hiddenClass)?o("paginationShow"):o("paginationHide"),a.forEach(e=>e.classList.toggle(i.params.pagination.hiddenClass))}});let w=()=>{i.el.classList.add(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList.add(i.params.pagination.paginationDisabledClass)),v()};Object.assign(i.pagination,{enable:()=>{i.el.classList.remove(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList.remove(i.params.pagination.paginationDisabledClass)),g(),m(),f()},disable:w,render:m,update:f,init:g,destroy:v})}function d(e){let t,i,r,a,l,n,o,d,p,u,{swiper:c,extendParams:h,on:f,emit:m,params:g}=e;c.autoplay={running:!1,paused:!1,timeLeft:0},h({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let v=g&&g.autoplay?g.autoplay.delay:3e3,w=g&&g.autoplay?g.autoplay.delay:3e3,b=new Date().getTime();function y(e){c&&!c.destroyed&&c.wrapperEl&&e.target===c.wrapperEl&&(c.wrapperEl.removeEventListener("transitionend",y),u||e.detail&&e.detail.bySwiperTouchMove||M())}let S=()=>{if(c.destroyed||!c.autoplay.running)return;c.autoplay.paused?a=!0:a&&(w=r,a=!1);let e=c.autoplay.paused?r:b+w-new Date().getTime();c.autoplay.timeLeft=e,m("autoplayTimeLeft",e,e/v),i=requestAnimationFrame(()=>{S()})},E=e=>{if(c.destroyed||!c.autoplay.running)return;cancelAnimationFrame(i),S();let s=void 0===e?c.params.autoplay.delay:e;v=c.params.autoplay.delay,w=c.params.autoplay.delay;let a=(()=>{let e;if(e=c.virtual&&c.params.virtual.enabled?c.slides.find(e=>e.classList.contains("swiper-slide-active")):c.slides[c.activeIndex])return parseInt(e.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(a)&&a>0&&void 0===e&&(s=a,v=a,w=a),r=s;let l=c.params.speed,n=()=>{c&&!c.destroyed&&(c.params.autoplay.reverseDirection?!c.isBeginning||c.params.loop||c.params.rewind?(c.slidePrev(l,!0,!0),m("autoplay")):c.params.autoplay.stopOnLastSlide||(c.slideTo(c.slides.length-1,l,!0,!0),m("autoplay")):!c.isEnd||c.params.loop||c.params.rewind?(c.slideNext(l,!0,!0),m("autoplay")):c.params.autoplay.stopOnLastSlide||(c.slideTo(0,l,!0,!0),m("autoplay")),c.params.cssMode&&(b=new Date().getTime(),requestAnimationFrame(()=>{E()})))};return s>0?(clearTimeout(t),t=setTimeout(()=>{n()},s)):requestAnimationFrame(()=>{n()}),s},T=()=>{b=new Date().getTime(),c.autoplay.running=!0,E(),m("autoplayStart")},x=()=>{c.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(i),m("autoplayStop")},C=(e,i)=>{if(c.destroyed||!c.autoplay.running)return;clearTimeout(t),e||(p=!0);let s=()=>{m("autoplayPause"),c.params.autoplay.waitForTransition?c.wrapperEl.addEventListener("transitionend",y):M()};if(c.autoplay.paused=!0,i){d&&(r=c.params.autoplay.delay),d=!1,s();return}r=(r||c.params.autoplay.delay)-(new Date().getTime()-b),c.isEnd&&r<0&&!c.params.loop||(r<0&&(r=0),s())},M=()=>{c.isEnd&&r<0&&!c.params.loop||c.destroyed||!c.autoplay.running||(b=new Date().getTime(),p?(p=!1,E(r)):E(),c.autoplay.paused=!1,m("autoplayResume"))},P=()=>{if(c.destroyed||!c.autoplay.running)return;let e=(0,s.g)();"hidden"===e.visibilityState&&(p=!0,C(!0)),"visible"===e.visibilityState&&M()},L=e=>{"mouse"===e.pointerType&&(p=!0,u=!0,c.animating||c.autoplay.paused||C(!0))},k=e=>{"mouse"===e.pointerType&&(u=!1,c.autoplay.paused&&M())};f("init",()=>{c.params.autoplay.enabled&&(c.params.autoplay.pauseOnMouseEnter&&(c.el.addEventListener("pointerenter",L),c.el.addEventListener("pointerleave",k)),(0,s.g)().addEventListener("visibilitychange",P),T())}),f("destroy",()=>{c.el&&"string"!=typeof c.el&&(c.el.removeEventListener("pointerenter",L),c.el.removeEventListener("pointerleave",k)),(0,s.g)().removeEventListener("visibilitychange",P),c.autoplay.running&&x()}),f("_freeModeStaticRelease",()=>{(n||p)&&M()}),f("_freeModeNoMomentumRelease",()=>{c.params.autoplay.disableOnInteraction?x():C(!0,!0)}),f("beforeTransitionStart",(e,t,i)=>{!c.destroyed&&c.autoplay.running&&(i||!c.params.autoplay.disableOnInteraction?C(!0,!0):x())}),f("sliderFirstMove",()=>{if(!c.destroyed&&c.autoplay.running){if(c.params.autoplay.disableOnInteraction)return void x();l=!0,n=!1,p=!1,o=setTimeout(()=>{p=!0,n=!0,C(!0)},200)}}),f("touchEnd",()=>{if(!c.destroyed&&c.autoplay.running&&l){if(clearTimeout(o),clearTimeout(t),c.params.autoplay.disableOnInteraction){n=!1,l=!1;return}n&&c.params.cssMode&&M(),n=!1,l=!1}}),f("slideChange",()=>{!c.destroyed&&c.autoplay.running&&(d=!0)}),Object.assign(c.autoplay,{start:T,stop:x,pause:C,resume:M})}function p(e){let{swiper:t,extendParams:i,on:a}=e;i({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let l=!1,n=!1;function o(){let e,i=t.thumbs.swiper;if(!i||i.destroyed)return;let s=i.clickedIndex,r=i.clickedSlide;r&&r.classList.contains(t.params.thumbs.slideThumbActiveClass)||null!=s&&(e=i.params.loop?parseInt(i.clickedSlide.getAttribute("data-swiper-slide-index"),10):s,t.params.loop?t.slideToLoop(e):t.slideTo(e))}function d(){let{thumbs:e}=t.params;if(l)return!1;l=!0;let i=t.constructor;if(e.swiper instanceof i){if(e.swiper.destroyed)return l=!1,!1;t.thumbs.swiper=e.swiper,Object.assign(t.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(t.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),t.thumbs.swiper.update()}else if((0,r.o)(e.swiper)){let s=Object.assign({},e.swiper);Object.assign(s,{watchSlidesProgress:!0,slideToClickedSlide:!1}),t.thumbs.swiper=new i(s),n=!0}return t.thumbs.swiper.el.classList.add(t.params.thumbs.thumbsContainerClass),t.thumbs.swiper.on("tap",o),!0}function p(e){let i=t.thumbs.swiper;if(!i||i.destroyed)return;let s="auto"===i.params.slidesPerView?i.slidesPerViewDynamic():i.params.slidesPerView,a=1,l=t.params.thumbs.slideThumbActiveClass;if(t.params.slidesPerView>1&&!t.params.centeredSlides&&(a=t.params.slidesPerView),t.params.thumbs.multipleActiveThumbs||(a=1),a=Math.floor(a),i.slides.forEach(e=>e.classList.remove(l)),i.params.loop||i.params.virtual&&i.params.virtual.enabled)for(let e=0;e<a;e+=1)(0,r.e)(i.slidesEl,`[data-swiper-slide-index="${t.realIndex+e}"]`).forEach(e=>{e.classList.add(l)});else for(let e=0;e<a;e+=1)i.slides[t.realIndex+e]&&i.slides[t.realIndex+e].classList.add(l);let n=t.params.thumbs.autoScrollOffset,o=n&&!i.params.loop;if(t.realIndex!==i.realIndex||o){let r,a,l=i.activeIndex;if(i.params.loop){let e=i.slides.find(e=>e.getAttribute("data-swiper-slide-index")===`${t.realIndex}`);r=i.slides.indexOf(e),a=t.activeIndex>t.previousIndex?"next":"prev"}else a=(r=t.realIndex)>t.previousIndex?"next":"prev";o&&(r+="next"===a?n:-1*n),i.visibleSlidesIndexes&&0>i.visibleSlidesIndexes.indexOf(r)&&(i.params.centeredSlides?r=r>l?r-Math.floor(s/2)+1:r+Math.floor(s/2)-1:r>l&&i.params.slidesPerGroup,i.slideTo(r,e?0:void 0))}}t.thumbs={swiper:null},a("beforeInit",()=>{let{thumbs:e}=t.params;if(e&&e.swiper)if("string"==typeof e.swiper||e.swiper instanceof HTMLElement){let i=(0,s.g)(),r=()=>{!t.destroyed&&((()=>{let s="string"==typeof e.swiper?i.querySelector(e.swiper):e.swiper;if(s&&s.swiper)e.swiper=s.swiper,d(),p(!0);else if(s){let i=`${t.params.eventsPrefix}init`,r=a=>{e.swiper=a.detail[0],s.removeEventListener(i,r),d(),p(!0),e.swiper.update(),t.update()};s.addEventListener(i,r)}return s})()||requestAnimationFrame(r))};requestAnimationFrame(r)}else d(),p(!0)}),a("slideChange update resize observerUpdate",()=>{p()}),a("setTransition",(e,i)=>{let s=t.thumbs.swiper;s&&!s.destroyed&&s.setTransition(i)}),a("beforeDestroy",()=>{let e=t.thumbs.swiper;e&&!e.destroyed&&n&&e.destroy()}),Object.assign(t.thumbs,{init:d,update:p})}function u(e,t,i){let s=`swiper-slide-shadow${i?`-${i}`:""}${e?` swiper-slide-shadow-${e}`:""}`,a=(0,r.g)(t),l=a.querySelector(`.${s.split(" ").join(".")}`);return l||(l=(0,r.c)("div",s.split(" ")),a.append(l)),l}function c(e){let t,{swiper:i,extendParams:s,on:a}=e;s({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}});let{effect:l,swiper:n,on:o,setTranslate:d,setTransition:p,overwriteParams:c,perspective:h,recreateShadows:f,getEffectParams:m}={effect:"coverflow",swiper:i,on:a,setTranslate:()=>{let{width:e,height:t,slides:s,slidesSizesGrid:a}=i,l=i.params.coverflowEffect,n=i.isHorizontal(),o=i.translate,d=n?-o+e/2:-o+t/2,p=n?l.rotate:-l.rotate,c=l.depth,h=(0,r.p)(i);for(let e=0,t=s.length;e<t;e+=1){let t=s[e],i=a[e],o=(d-t.swiperSlideOffset-i/2)/i,f="function"==typeof l.modifier?l.modifier(o):o*l.modifier,m=n?p*f:0,g=n?0:p*f,v=-c*Math.abs(f),w=l.stretch;"string"==typeof w&&-1!==w.indexOf("%")&&(w=parseFloat(l.stretch)/100*i);let b=n?0:w*f,y=n?w*f:0,S=1-(1-l.scale)*Math.abs(f);.001>Math.abs(y)&&(y=0),.001>Math.abs(b)&&(b=0),.001>Math.abs(v)&&(v=0),.001>Math.abs(m)&&(m=0),.001>Math.abs(g)&&(g=0),.001>Math.abs(S)&&(S=0);let E=`translate3d(${y}px,${b}px,${v}px)  rotateX(${h(g)}deg) rotateY(${h(m)}deg) scale(${S})`;if(function(e,t){let i=(0,r.g)(t);return i!==t&&(i.style.backfaceVisibility="hidden",i.style["-webkit-backface-visibility"]="hidden"),i}(0,t).style.transform=E,t.style.zIndex=-Math.abs(Math.round(f))+1,l.slideShadows){let e=n?t.querySelector(".swiper-slide-shadow-left"):t.querySelector(".swiper-slide-shadow-top"),i=n?t.querySelector(".swiper-slide-shadow-right"):t.querySelector(".swiper-slide-shadow-bottom");e||(e=u("coverflow",t,n?"left":"top")),i||(i=u("coverflow",t,n?"right":"bottom")),e&&(e.style.opacity=f>0?f:0),i&&(i.style.opacity=-f>0?-f:0)}}},setTransition:e=>{i.slides.map(e=>(0,r.g)(e)).forEach(t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(t=>{t.style.transitionDuration=`${e}ms`})})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})};o("beforeInit",()=>{if(n.params.effect!==l)return;n.classNames.push(`${n.params.containerModifierClass}${l}`),h&&h()&&n.classNames.push(`${n.params.containerModifierClass}3d`);let e=c?c():{};Object.assign(n.params,e),Object.assign(n.originalParams,e)}),o("setTranslate _virtualUpdated",()=>{n.params.effect===l&&d()}),o("setTransition",(e,t)=>{n.params.effect===l&&p(t)}),o("transitionEnd",()=>{n.params.effect===l&&f&&m&&m().slideShadows&&(n.slides.forEach(e=>{e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(e=>e.remove())}),f())}),o("virtualUpdate",()=>{n.params.effect===l&&(n.slides.length||(t=!0),requestAnimationFrame(()=>{t&&n.slides&&n.slides.length&&(d(),t=!1)}))})}},4569:(e,t,i)=>{"use strict";i.d(t,{a:()=>d,b:()=>y,c:()=>m,e:()=>c,f:()=>l,g:()=>u,h:()=>S,i:()=>b,k:()=>n,m:()=>E,n:()=>a,o:()=>o,p:()=>T,q:()=>w,r:()=>v,s:()=>x,t:()=>g,u:()=>p,v:()=>f,w:()=>h,x:()=>function e(){let t=Object(arguments.length<=0?void 0:arguments[0]),i=["__proto__","constructor","prototype"];for(let s=1;s<arguments.length;s+=1){let r=s<0||arguments.length<=s?void 0:arguments[s];if(null!=r&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(r instanceof HTMLElement):!r||1!==r.nodeType&&11!==r.nodeType)){let s=Object.keys(Object(r)).filter(e=>0>i.indexOf(e));for(let i=0,a=s.length;i<a;i+=1){let a=s[i],l=Object.getOwnPropertyDescriptor(r,a);void 0!==l&&l.enumerable&&(o(t[a])&&o(r[a])?r[a].__swiper__?t[a]=r[a]:e(t[a],r[a]):!o(t[a])&&o(r[a])?(t[a]={},r[a].__swiper__?t[a]=r[a]:e(t[a],r[a])):t[a]=r[a])}}}return t},y:()=>r});var s=i(2370);function r(e){Object.keys(e).forEach(t=>{try{e[t]=null}catch(e){}try{delete e[t]}catch(e){}})}function a(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function l(){return Date.now()}function n(e,t){let i,r,a;void 0===t&&(t="x");let l=(0,s.a)(),n=function(e){let t,i=(0,s.a)();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return l.WebKitCSSMatrix?((r=n.transform||n.webkitTransform).split(",").length>6&&(r=r.split(", ").map(e=>e.replace(",",".")).join(", ")),a=new l.WebKitCSSMatrix("none"===r?"":r)):i=(a=n.MozTransform||n.OTransform||n.MsTransform||n.msTransform||n.transform||n.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(r=l.WebKitCSSMatrix?a.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(r=l.WebKitCSSMatrix?a.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),r||0}function o(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function d(e,t,i){e.style.setProperty(t,i)}function p(e){let t,{swiper:i,targetPosition:r,side:a}=e,l=(0,s.a)(),n=-i.translate,o=null,d=i.params.speed;i.wrapperEl.style.scrollSnapType="none",l.cancelAnimationFrame(i.cssModeFrameID);let p=r>n?"next":"prev",u=(e,t)=>"next"===p&&e>=t||"prev"===p&&e<=t,c=()=>{t=new Date().getTime(),null===o&&(o=t);let e=n+(.5-Math.cos(Math.max(Math.min((t-o)/d,1),0)*Math.PI)/2)*(r-n);if(u(e,r)&&(e=r),i.wrapperEl.scrollTo({[a]:e}),u(e,r)){i.wrapperEl.style.overflow="hidden",i.wrapperEl.style.scrollSnapType="",setTimeout(()=>{i.wrapperEl.style.overflow="",i.wrapperEl.scrollTo({[a]:e})}),l.cancelAnimationFrame(i.cssModeFrameID);return}i.cssModeFrameID=l.requestAnimationFrame(c)};c()}function u(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function c(e,t){void 0===t&&(t="");let i=(0,s.a)(),r=[...e.children];return(i.HTMLSlotElement&&e instanceof HTMLSlotElement&&r.push(...e.assignedElements()),t)?r.filter(e=>e.matches(t)):r}function h(e,t){let i=(0,s.a)(),r=t.contains(e);return!r&&i.HTMLSlotElement&&t instanceof HTMLSlotElement&&((r=[...t.assignedElements()].includes(e))||(r=function(e,t){let i=[t];for(;i.length>0;){let t=i.shift();if(e===t)return!0;i.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t))),r}function f(e){try{console.warn(e);return}catch(e){}}function m(e,t){var i;void 0===t&&(t=[]);let s=document.createElement(e);return s.classList.add(...Array.isArray(t)?t:(void 0===(i=t)&&(i=""),i.trim().split(" ").filter(e=>!!e.trim()))),s}function g(e,t){let i=[];for(;e.previousElementSibling;){let s=e.previousElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function v(e,t){let i=[];for(;e.nextElementSibling;){let s=e.nextElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function w(e,t){return(0,s.a)().getComputedStyle(e,null).getPropertyValue(t)}function b(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function y(e,t){let i=[],s=e.parentElement;for(;s;)t?s.matches(t)&&i.push(s):i.push(s),s=s.parentElement;return i}function S(e,t,i){let r=(0,s.a)();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function E(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}function T(e){return t=>Math.abs(t)>0&&e.browser&&e.browser.need3dFix&&Math.abs(t)%90==0?t+.001:t}function x(e,t){void 0===t&&(t=""),"undefined"!=typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}},6667:()=>{},7328:(e,t,i)=>{e.exports=i(9836)},7888:(e,t,i)=>{"use strict";let s,r,a;i.d(t,{RC:()=>H,qr:()=>W});var l=i(4232),n=i(2370),o=i(4569);function d(){return s||(s=function(){let e=(0,n.a)(),t=(0,n.g)();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),s}function p(e){return void 0===e&&(e={}),r||(r=function(e){let{userAgent:t}=void 0===e?{}:e,i=d(),s=(0,n.a)(),r=s.navigator.platform,a=t||s.navigator.userAgent,l={ios:!1,android:!1},o=s.screen.width,p=s.screen.height,u=a.match(/(Android);?[\s\/]+([\d.]+)?/),c=a.match(/(iPad).*OS\s([\d_]+)/),h=a.match(/(iPod)(.*OS\s([\d_]+))?/),f=!c&&a.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m="MacIntel"===r;return!c&&m&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${p}`)>=0&&((c=a.match(/(Version)\/([\d.]+)/))||(c=[0,1,"13_0_0"]),m=!1),u&&"Win32"!==r&&(l.os="android",l.android=!0),(c||f||h)&&(l.os="ios",l.ios=!0),l}(e)),r}function u(){return a||(a=function(){let e=(0,n.a)(),t=p(),i=!1;function s(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(s()){let t=String(e.navigator.userAgent);if(t.includes("Version/")){let[e,s]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));i=e<16||16===e&&s<2}}let r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),a=s(),l=a||r&&t.ios;return{isSafari:i||a,needPerspectiveFix:i,need3dFix:l,isWebView:r}}()),a}let c=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},h=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},f=(e,t)=>{if(!e||e.destroyed||!e.params)return;let i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},m=(e,t)=>{if(!e.slides[t])return;let i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},g=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);let s="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let i=[r-t];i.push(...Array.from({length:t}).map((e,t)=>r+s+t)),e.slides.forEach((t,s)=>{i.includes(t.column)&&m(e,s)});return}let a=r+s-1;if(e.params.rewind||e.params.loop)for(let s=r-t;s<=a+t;s+=1){let t=(s%i+i)%i;(t<r||t>a)&&m(e,t)}else for(let s=Math.max(r-t,0);s<=Math.min(a+t,i-1);s+=1)s!==r&&(s>a||s<r)&&m(e,s)};function v(e){let{swiper:t,runCallbacks:i,direction:s,step:r}=e,{activeIndex:a,previousIndex:l}=t,n=s;n||(n=a>l?"next":a<l?"prev":"reset"),t.emit(`transition${r}`),i&&"reset"===n?t.emit(`slideResetTransition${r}`):i&&a!==l&&(t.emit(`slideChangeTransition${r}`),"next"===n?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`))}function w(e,t,i){let s=(0,n.a)(),{params:r}=e,a=r.edgeSwipeDetection,l=r.edgeSwipeThreshold;return!a||!(i<=l)&&!(i>=s.innerWidth-l)||"prevent"===a&&(t.preventDefault(),!0)}function b(e){let t=(0,n.g)(),i=e;i.originalEvent&&(i=i.originalEvent);let s=this.touchEventsData;if("pointerdown"===i.type){if(null!==s.pointerId&&s.pointerId!==i.pointerId)return;s.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(s.touchId=i.targetTouches[0].identifier);if("touchstart"===i.type)return void w(this,i,i.targetTouches[0].pageX);let{params:r,touches:a,enabled:l}=this;if(!l||!r.simulateTouch&&"mouse"===i.pointerType||this.animating&&r.preventInteractionOnTransition)return;!this.animating&&r.cssMode&&r.loop&&this.loopFix();let d=i.target;if("wrapper"===r.touchEventsTarget&&!(0,o.w)(d,this.wrapperEl)||"which"in i&&3===i.which||"button"in i&&i.button>0||s.isTouched&&s.isMoved)return;let p=!!r.noSwipingClass&&""!==r.noSwipingClass,u=i.composedPath?i.composedPath():i.path;p&&i.target&&i.target.shadowRoot&&u&&(d=u[0]);let c=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,h=!!(i.target&&i.target.shadowRoot);if(r.noSwiping&&(h?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===(0,n.g)()||i===(0,n.a)())return null;i.assignedSlot&&(i=i.assignedSlot);let s=i.closest(e);return s||i.getRootNode?s||t(i.getRootNode().host):null}(t)}(c,d):d.closest(c))){this.allowClick=!0;return}if(r.swipeHandler&&!d.closest(r.swipeHandler))return;a.currentX=i.pageX,a.currentY=i.pageY;let f=a.currentX,m=a.currentY;if(!w(this,i,f))return;Object.assign(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=f,a.startY=m,s.touchStartTime=(0,o.f)(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,r.threshold>0&&(s.allowThresholdMove=!1);let g=!0;d.matches(s.focusableElements)&&(g=!1,"SELECT"===d.nodeName&&(s.isTouched=!1)),t.activeElement&&t.activeElement.matches(s.focusableElements)&&t.activeElement!==d&&("mouse"===i.pointerType||"mouse"!==i.pointerType&&!d.matches(s.focusableElements))&&t.activeElement.blur();let v=g&&this.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||v)&&!d.isContentEditable&&i.preventDefault(),r.freeMode&&r.freeMode.enabled&&this.freeMode&&this.animating&&!r.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",i)}function y(e){let t,i,s=(0,n.g)(),r=this.touchEventsData,{params:a,touches:l,rtlTranslate:d,enabled:p}=this;if(!p||!a.simulateTouch&&"mouse"===e.pointerType)return;let u=e;if(u.originalEvent&&(u=u.originalEvent),"pointermove"===u.type&&(null!==r.touchId||u.pointerId!==r.pointerId))return;if("touchmove"===u.type){if(!(t=[...u.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else t=u;if(!r.isTouched){r.startMoving&&r.isScrolling&&this.emit("touchMoveOpposite",u);return}let c=t.pageX,h=t.pageY;if(u.preventedByNestedSwiper){l.startX=c,l.startY=h;return}if(!this.allowTouchMove){u.target.matches(r.focusableElements)||(this.allowClick=!1),r.isTouched&&(Object.assign(l,{startX:c,startY:h,currentX:c,currentY:h}),r.touchStartTime=(0,o.f)());return}if(a.touchReleaseOnEdges&&!a.loop){if(this.isVertical()){if(h<l.startY&&this.translate<=this.maxTranslate()||h>l.startY&&this.translate>=this.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else if(d&&(c>l.startX&&-this.translate<=this.maxTranslate()||c<l.startX&&-this.translate>=this.minTranslate()))return;else if(!d&&(c<l.startX&&this.translate<=this.maxTranslate()||c>l.startX&&this.translate>=this.minTranslate()))return}if(s.activeElement&&s.activeElement.matches(r.focusableElements)&&s.activeElement!==u.target&&"mouse"!==u.pointerType&&s.activeElement.blur(),s.activeElement&&u.target===s.activeElement&&u.target.matches(r.focusableElements)){r.isMoved=!0,this.allowClick=!1;return}r.allowTouchCallbacks&&this.emit("touchMove",u),l.previousX=l.currentX,l.previousY=l.currentY,l.currentX=c,l.currentY=h;let f=l.currentX-l.startX,m=l.currentY-l.startY;if(this.params.threshold&&Math.sqrt(f**2+m**2)<this.params.threshold)return;if(void 0===r.isScrolling){let e;this.isHorizontal()&&l.currentY===l.startY||this.isVertical()&&l.currentX===l.startX?r.isScrolling=!1:f*f+m*m>=25&&(e=180*Math.atan2(Math.abs(m),Math.abs(f))/Math.PI,r.isScrolling=this.isHorizontal()?e>a.touchAngle:90-e>a.touchAngle)}if(r.isScrolling&&this.emit("touchMoveOpposite",u),void 0===r.startMoving&&(l.currentX!==l.startX||l.currentY!==l.startY)&&(r.startMoving=!0),r.isScrolling||"touchmove"===u.type&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;this.allowClick=!1,!a.cssMode&&u.cancelable&&u.preventDefault(),a.touchMoveStopPropagation&&!a.nested&&u.stopPropagation();let g=this.isHorizontal()?f:m,v=this.isHorizontal()?l.currentX-l.previousX:l.currentY-l.previousY;a.oneWayMovement&&(g=Math.abs(g)*(d?1:-1),v=Math.abs(v)*(d?1:-1)),l.diff=g,g*=a.touchRatio,d&&(g=-g,v=-v);let w=this.touchesDirection;this.swipeDirection=g>0?"prev":"next",this.touchesDirection=v>0?"prev":"next";let b=this.params.loop&&!a.cssMode,y="next"===this.touchesDirection&&this.allowSlideNext||"prev"===this.touchesDirection&&this.allowSlidePrev;if(!r.isMoved){if(b&&y&&this.loopFix({direction:this.swipeDirection}),r.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});this.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,a.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",u)}if(new Date().getTime(),!1!==a._loopSwapReset&&r.isMoved&&r.allowThresholdMove&&w!==this.touchesDirection&&b&&y&&Math.abs(g)>=1){Object.assign(l,{startX:c,startY:h,currentX:c,currentY:h,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}this.emit("sliderMove",u),r.isMoved=!0,r.currentTranslate=g+r.startTranslate;let S=!0,E=a.resistanceRatio;if(a.touchReleaseOnEdges&&(E=0),g>0?(b&&y&&!i&&r.allowThresholdMove&&r.currentTranslate>(a.centeredSlides?this.minTranslate()-this.slidesSizesGrid[this.activeIndex+1]-("auto"!==a.slidesPerView&&this.slides.length-a.slidesPerView>=2?this.slidesSizesGrid[this.activeIndex+1]+this.params.spaceBetween:0)-this.params.spaceBetween:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>this.minTranslate()&&(S=!1,a.resistance&&(r.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+r.startTranslate+g)**E))):g<0&&(b&&y&&!i&&r.allowThresholdMove&&r.currentTranslate<(a.centeredSlides?this.maxTranslate()+this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween+("auto"!==a.slidesPerView&&this.slides.length-a.slidesPerView>=2?this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween:0):this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===a.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(a.slidesPerView,10)))}),r.currentTranslate<this.maxTranslate()&&(S=!1,a.resistance&&(r.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-r.startTranslate-g)**E))),S&&(u.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),this.allowSlidePrev||this.allowSlideNext||(r.currentTranslate=r.startTranslate),a.threshold>0)if(Math.abs(g)>a.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,l.startX=l.currentX,l.startY=l.currentY,r.currentTranslate=r.startTranslate,l.diff=this.isHorizontal()?l.currentX-l.startX:l.currentY-l.startY;return}}else{r.currentTranslate=r.startTranslate;return}a.followFinger&&!a.cssMode&&((a.freeMode&&a.freeMode.enabled&&this.freeMode||a.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),a.freeMode&&a.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(r.currentTranslate),this.setTranslate(r.currentTranslate))}function S(e){let t,i,s=this,r=s.touchEventsData,a=e;if(a.originalEvent&&(a=a.originalEvent),"touchend"===a.type||"touchcancel"===a.type){if(!(t=[...a.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else{if(null!==r.touchId||a.pointerId!==r.pointerId)return;t=a}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(a.type)&&!(["pointercancel","contextmenu"].includes(a.type)&&(s.browser.isSafari||s.browser.isWebView)))return;r.pointerId=null,r.touchId=null;let{params:l,touches:n,rtlTranslate:d,slidesGrid:p,enabled:u}=s;if(!u||!l.simulateTouch&&"mouse"===a.pointerType)return;if(r.allowTouchCallbacks&&s.emit("touchEnd",a),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&l.grabCursor&&s.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}l.grabCursor&&r.isMoved&&r.isTouched&&(!0===s.allowSlideNext||!0===s.allowSlidePrev)&&s.setGrabCursor(!1);let c=(0,o.f)(),h=c-r.touchStartTime;if(s.allowClick){let e=a.path||a.composedPath&&a.composedPath();s.updateClickedSlide(e&&e[0]||a.target,e),s.emit("tap click",a),h<300&&c-r.lastClickTime<300&&s.emit("doubleTap doubleClick",a)}if(r.lastClickTime=(0,o.f)(),(0,o.n)(()=>{s.destroyed||(s.allowClick=!0)}),!r.isTouched||!r.isMoved||!s.swipeDirection||0===n.diff&&!r.loopSwapReset||r.currentTranslate===r.startTranslate&&!r.loopSwapReset){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}if(r.isTouched=!1,r.isMoved=!1,r.startMoving=!1,i=l.followFinger?d?s.translate:-s.translate:-r.currentTranslate,l.cssMode)return;if(l.freeMode&&l.freeMode.enabled)return void s.freeMode.onTouchEnd({currentPos:i});let f=i>=-s.maxTranslate()&&!s.params.loop,m=0,g=s.slidesSizesGrid[0];for(let e=0;e<p.length;e+=e<l.slidesPerGroupSkip?1:l.slidesPerGroup){let t=e<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;void 0!==p[e+t]?(f||i>=p[e]&&i<p[e+t])&&(m=e,g=p[e+t]-p[e]):(f||i>=p[e])&&(m=e,g=p[p.length-1]-p[p.length-2])}let v=null,w=null;l.rewind&&(s.isBeginning?w=l.virtual&&l.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1:s.isEnd&&(v=0));let b=(i-p[m])/g,y=m<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;if(h>l.longSwipesMs){if(!l.longSwipes)return void s.slideTo(s.activeIndex);"next"===s.swipeDirection&&(b>=l.longSwipesRatio?s.slideTo(l.rewind&&s.isEnd?v:m+y):s.slideTo(m)),"prev"===s.swipeDirection&&(b>1-l.longSwipesRatio?s.slideTo(m+y):null!==w&&b<0&&Math.abs(b)>l.longSwipesRatio?s.slideTo(w):s.slideTo(m))}else{if(!l.shortSwipes)return void s.slideTo(s.activeIndex);s.navigation&&(a.target===s.navigation.nextEl||a.target===s.navigation.prevEl)?a.target===s.navigation.nextEl?s.slideTo(m+y):s.slideTo(m):("next"===s.swipeDirection&&s.slideTo(null!==v?v:m+y),"prev"===s.swipeDirection&&s.slideTo(null!==w?w:m))}}function E(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:s,allowSlidePrev:r,snapGrid:a}=e,l=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let n=l&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||n?e.params.loop&&!l?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=r,e.allowSlideNext=s,e.params.watchOverflow&&a!==e.snapGrid&&e.checkOverflow()}function T(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function x(){let{wrapperEl:e,rtlTranslate:t,enabled:i}=this;if(!i)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-e.scrollLeft:this.translate=-e.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let s=this.maxTranslate()-this.minTranslate();(0===s?0:(this.translate-this.minTranslate())/s)!==this.progress&&this.updateProgress(t?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function C(e){f(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function M(){!this.documentTouchHandlerProceeded&&(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}let P=(e,t)=>{let i=(0,n.g)(),{params:s,el:r,wrapperEl:a,device:l}=e,o=!!s.nested,d="on"===t?"addEventListener":"removeEventListener";r&&"string"!=typeof r&&(i[d]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),r[d]("touchstart",e.onTouchStart,{passive:!1}),r[d]("pointerdown",e.onTouchStart,{passive:!1}),i[d]("touchmove",e.onTouchMove,{passive:!1,capture:o}),i[d]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[d]("touchend",e.onTouchEnd,{passive:!0}),i[d]("pointerup",e.onTouchEnd,{passive:!0}),i[d]("pointercancel",e.onTouchEnd,{passive:!0}),i[d]("touchcancel",e.onTouchEnd,{passive:!0}),i[d]("pointerout",e.onTouchEnd,{passive:!0}),i[d]("pointerleave",e.onTouchEnd,{passive:!0}),i[d]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[d]("click",e.onClick,!0),s.cssMode&&a[d]("scroll",e.onScroll),s.updateOnWindowResize?e[t](l.ios||l.android?"resize orientationchange observerUpdate":"resize observerUpdate",E,!0):e[t]("observerUpdate",E,!0),r[d]("load",e.onLoad,{capture:!0}))},L=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var k={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let O={eventsEmitter:{on(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;let r=i?"unshift":"push";return e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][r](t)}),s},once(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;function r(){s.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var i=arguments.length,a=Array(i),l=0;l<i;l++)a[l]=arguments[l];t.apply(s,a)}return r.__emitterProxy=t,s.on(e,r,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((s,r)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(r,1)})}),i},emit(){let e,t,i,s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;for(var r=arguments.length,a=Array(r),l=0;l<r;l++)a[l]=arguments[l];return"string"==typeof a[0]||Array.isArray(a[0])?(e=a[0],t=a.slice(1,a.length),i=s):(e=a[0].events,t=a[0].data,i=a[0].context||s),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(e=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(s=>{s.apply(i,[e,...t])}),s.eventsListeners&&s.eventsListeners[e]&&s.eventsListeners[e].forEach(e=>{e.apply(i,t)})}),s}},update:{updateSize:function(){let e,t,i=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt((0,o.q)(i,"padding-left")||0,10)-parseInt((0,o.q)(i,"padding-right")||0,10),t=t-parseInt((0,o.q)(i,"padding-top")||0,10)-parseInt((0,o.q)(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e,t=this;function i(e,i){return parseFloat(e.getPropertyValue(t.getDirectionLabel(i))||0)}let s=t.params,{wrapperEl:r,slidesEl:a,size:l,rtlTranslate:n,wrongRTL:d}=t,p=t.virtual&&s.virtual.enabled,u=p?t.virtual.slides.length:t.slides.length,c=(0,o.e)(a,`.${t.params.slideClass}, swiper-slide`),h=p?t.virtual.slides.length:c.length,f=[],m=[],g=[],v=s.slidesOffsetBefore;"function"==typeof v&&(v=s.slidesOffsetBefore.call(t));let w=s.slidesOffsetAfter;"function"==typeof w&&(w=s.slidesOffsetAfter.call(t));let b=t.snapGrid.length,y=t.slidesGrid.length,S=s.spaceBetween,E=-v,T=0,x=0;if(void 0===l)return;"string"==typeof S&&S.indexOf("%")>=0?S=parseFloat(S.replace("%",""))/100*l:"string"==typeof S&&(S=parseFloat(S)),t.virtualSize=-S,c.forEach(e=>{n?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),s.centeredSlides&&s.cssMode&&((0,o.a)(r,"--swiper-centered-offset-before",""),(0,o.a)(r,"--swiper-centered-offset-after",""));let C=s.grid&&s.grid.rows>1&&t.grid;C?t.grid.initSlides(c):t.grid&&t.grid.unsetSlides();let M="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter(e=>void 0!==s.breakpoints[e].slidesPerView).length>0;for(let r=0;r<h;r+=1){let a;if(e=0,c[r]&&(a=c[r]),C&&t.grid.updateSlide(r,a,c),!c[r]||"none"!==(0,o.q)(a,"display")){if("auto"===s.slidesPerView){M&&(c[r].style[t.getDirectionLabel("width")]="");let l=getComputedStyle(a),n=a.style.transform,d=a.style.webkitTransform;if(n&&(a.style.transform="none"),d&&(a.style.webkitTransform="none"),s.roundLengths)e=t.isHorizontal()?(0,o.h)(a,"width",!0):(0,o.h)(a,"height",!0);else{let t=i(l,"width"),s=i(l,"padding-left"),r=i(l,"padding-right"),n=i(l,"margin-left"),o=i(l,"margin-right"),d=l.getPropertyValue("box-sizing");if(d&&"border-box"===d)e=t+n+o;else{let{clientWidth:i,offsetWidth:l}=a;e=t+s+r+n+o+(l-i)}}n&&(a.style.transform=n),d&&(a.style.webkitTransform=d),s.roundLengths&&(e=Math.floor(e))}else e=(l-(s.slidesPerView-1)*S)/s.slidesPerView,s.roundLengths&&(e=Math.floor(e)),c[r]&&(c[r].style[t.getDirectionLabel("width")]=`${e}px`);c[r]&&(c[r].swiperSlideSize=e),g.push(e),s.centeredSlides?(E=E+e/2+T/2+S,0===T&&0!==r&&(E=E-l/2-S),0===r&&(E=E-l/2-S),.001>Math.abs(E)&&(E=0),s.roundLengths&&(E=Math.floor(E)),x%s.slidesPerGroup==0&&f.push(E),m.push(E)):(s.roundLengths&&(E=Math.floor(E)),(x-Math.min(t.params.slidesPerGroupSkip,x))%t.params.slidesPerGroup==0&&f.push(E),m.push(E),E=E+e+S),t.virtualSize+=e+S,T=e,x+=1}}if(t.virtualSize=Math.max(t.virtualSize,l)+w,n&&d&&("slide"===s.effect||"coverflow"===s.effect)&&(r.style.width=`${t.virtualSize+S}px`),s.setWrapperSize&&(r.style[t.getDirectionLabel("width")]=`${t.virtualSize+S}px`),C&&t.grid.updateWrapperSize(e,f),!s.centeredSlides){let e=[];for(let i=0;i<f.length;i+=1){let r=f[i];s.roundLengths&&(r=Math.floor(r)),f[i]<=t.virtualSize-l&&e.push(r)}f=e,Math.floor(t.virtualSize-l)-Math.floor(f[f.length-1])>1&&f.push(t.virtualSize-l)}if(p&&s.loop){let e=g[0]+S;if(s.slidesPerGroup>1){let i=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/s.slidesPerGroup),r=e*s.slidesPerGroup;for(let e=0;e<i;e+=1)f.push(f[f.length-1]+r)}for(let i=0;i<t.virtual.slidesBefore+t.virtual.slidesAfter;i+=1)1===s.slidesPerGroup&&f.push(f[f.length-1]+e),m.push(m[m.length-1]+e),t.virtualSize+=e}if(0===f.length&&(f=[0]),0!==S){let e=t.isHorizontal()&&n?"marginLeft":t.getDirectionLabel("marginRight");c.filter((e,t)=>!s.cssMode||!!s.loop||t!==c.length-1).forEach(t=>{t.style[e]=`${S}px`})}if(s.centeredSlides&&s.centeredSlidesBounds){let e=0;g.forEach(t=>{e+=t+(S||0)});let t=(e-=S)>l?e-l:0;f=f.map(e=>e<=0?-v:e>t?t+w:e)}if(s.centerInsufficientSlides){let e=0;g.forEach(t=>{e+=t+(S||0)}),e-=S;let t=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(e+t<l){let i=(l-e-t)/2;f.forEach((e,t)=>{f[t]=e-i}),m.forEach((e,t)=>{m[t]=e+i})}}if(Object.assign(t,{slides:c,snapGrid:f,slidesGrid:m,slidesSizesGrid:g}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){(0,o.a)(r,"--swiper-centered-offset-before",`${-f[0]}px`),(0,o.a)(r,"--swiper-centered-offset-after",`${t.size/2-g[g.length-1]/2}px`);let e=-t.snapGrid[0],i=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+i)}if(h!==u&&t.emit("slidesLengthChange"),f.length!==b&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),m.length!==y&&t.emit("slidesGridLengthChange"),s.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!p&&!s.cssMode&&("slide"===s.effect||"fade"===s.effect)){let e=`${s.containerModifierClass}backface-hidden`,i=t.el.classList.contains(e);h<=s.maxBackfaceHiddenSlides?i||t.el.classList.add(e):i&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t,i=this,s=[],r=i.virtual&&i.params.virtual.enabled,a=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);let l=e=>r?i.slides[i.getSlideIndexByData(e)]:i.slides[e];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1)if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(e=>{s.push(e)});else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){let e=i.activeIndex+t;if(e>i.slides.length&&!r)break;s.push(l(e))}else s.push(l(i.activeIndex));for(t=0;t<s.length;t+=1)if(void 0!==s[t]){let e=s[t].offsetHeight;a=e>a?e:a}(a||0===a)&&(i.wrapperEl.style.height=`${a}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(this.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:i,rtlTranslate:s,snapGrid:r}=this;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&this.updateSlidesOffset();let a=-e;s&&(a=e),this.visibleSlidesIndexes=[],this.visibleSlides=[];let l=t.spaceBetween;"string"==typeof l&&l.indexOf("%")>=0?l=parseFloat(l.replace("%",""))/100*this.size:"string"==typeof l&&(l=parseFloat(l));for(let e=0;e<i.length;e+=1){let n=i[e],o=n.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(o-=i[0].swiperSlideOffset);let d=(a+(t.centeredSlides?this.minTranslate():0)-o)/(n.swiperSlideSize+l),p=(a-r[0]+(t.centeredSlides?this.minTranslate():0)-o)/(n.swiperSlideSize+l),u=-(a-o),h=u+this.slidesSizesGrid[e],f=u>=0&&u<=this.size-this.slidesSizesGrid[e],m=u>=0&&u<this.size-1||h>1&&h<=this.size||u<=0&&h>=this.size;m&&(this.visibleSlides.push(n),this.visibleSlidesIndexes.push(e)),c(n,m,t.slideVisibleClass),c(n,f,t.slideFullyVisibleClass),n.progress=s?-d:d,n.originalProgress=s?-p:p}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,i=this.maxTranslate()-this.minTranslate(),{progress:s,isBeginning:r,isEnd:a,progressLoop:l}=this,n=r,o=a;if(0===i)s=0,r=!0,a=!0;else{s=(e-this.minTranslate())/i;let t=1>Math.abs(e-this.minTranslate()),l=1>Math.abs(e-this.maxTranslate());r=t||s<=0,a=l||s>=1,t&&(s=0),l&&(s=1)}if(t.loop){let t=this.getSlideIndexByData(0),i=this.getSlideIndexByData(this.slides.length-1),s=this.slidesGrid[t],r=this.slidesGrid[i],a=this.slidesGrid[this.slidesGrid.length-1],n=Math.abs(e);(l=n>=s?(n-s)/a:(n+a-r)/a)>1&&(l-=1)}Object.assign(this,{progress:s,progressLoop:l,isBeginning:r,isEnd:a}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),r&&!n&&this.emit("reachBeginning toEdge"),a&&!o&&this.emit("reachEnd toEdge"),(n&&!r||o&&!a)&&this.emit("fromEdge"),this.emit("progress",s)},updateSlidesClasses:function(){let e,t,i,{slides:s,params:r,slidesEl:a,activeIndex:l}=this,n=this.virtual&&r.virtual.enabled,d=this.grid&&r.grid&&r.grid.rows>1,p=e=>(0,o.e)(a,`.${r.slideClass}${e}, swiper-slide${e}`)[0];if(n)if(r.loop){let t=l-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=p(`[data-swiper-slide-index="${t}"]`)}else e=p(`[data-swiper-slide-index="${l}"]`);else d?(e=s.find(e=>e.column===l),i=s.find(e=>e.column===l+1),t=s.find(e=>e.column===l-1)):e=s[l];e&&!d&&(i=(0,o.r)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop&&!i&&(i=s[0]),t=(0,o.t)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop),s.forEach(s=>{h(s,s===e,r.slideActiveClass),h(s,s===i,r.slideNextClass),h(s,s===t,r.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function(e){let t,i,s=this,r=s.rtlTranslate?s.translate:-s.translate,{snapGrid:a,params:l,activeIndex:n,realIndex:o,snapIndex:d}=s,p=e,u=e=>{let t=e-s.virtual.slidesBefore;return t<0&&(t=s.virtual.slides.length+t),t>=s.virtual.slides.length&&(t-=s.virtual.slides.length),t};if(void 0===p&&(p=function(e){let t,{slidesGrid:i,params:s}=e,r=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<i.length;e+=1)void 0!==i[e+1]?r>=i[e]&&r<i[e+1]-(i[e+1]-i[e])/2?t=e:r>=i[e]&&r<i[e+1]&&(t=e+1):r>=i[e]&&(t=e);return s.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(s)),a.indexOf(r)>=0)t=a.indexOf(r);else{let e=Math.min(l.slidesPerGroupSkip,p);t=e+Math.floor((p-e)/l.slidesPerGroup)}if(t>=a.length&&(t=a.length-1),p===n&&!s.params.loop){t!==d&&(s.snapIndex=t,s.emit("snapIndexChange"));return}if(p===n&&s.params.loop&&s.virtual&&s.params.virtual.enabled){s.realIndex=u(p);return}let c=s.grid&&l.grid&&l.grid.rows>1;if(s.virtual&&l.virtual.enabled&&l.loop)i=u(p);else if(c){let e=s.slides.find(e=>e.column===p),t=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(t)&&(t=Math.max(s.slides.indexOf(e),0)),i=Math.floor(t/l.grid.rows)}else if(s.slides[p]){let e=s.slides[p].getAttribute("data-swiper-slide-index");i=e?parseInt(e,10):p}else i=p;Object.assign(s,{previousSnapIndex:d,snapIndex:t,previousRealIndex:o,realIndex:i,previousIndex:n,activeIndex:p}),s.initialized&&g(s),s.emit("activeIndexChange"),s.emit("snapIndexChange"),(s.initialized||s.params.runCallbacksOnInit)&&(o!==i&&s.emit("realIndexChange"),s.emit("slideChange"))},updateClickedSlide:function(e,t){let i,s=this.params,r=e.closest(`.${s.slideClass}, swiper-slide`);!r&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!r&&e.matches&&e.matches(`.${s.slideClass}, swiper-slide`)&&(r=e)});let a=!1;if(r){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===r){a=!0,i=e;break}}if(r&&a)this.clickedSlide=r,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=i;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}s.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:i,translate:s,wrapperEl:r}=this;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;let a=(0,o.k)(r,e);return a+=this.cssOverflowAdjustment(),i&&(a=-a),a||0},setTranslate:function(e,t){let{rtlTranslate:i,params:s,wrapperEl:r,progress:a}=this,l=0,n=0;this.isHorizontal()?l=i?-e:e:n=e,s.roundLengths&&(l=Math.floor(l),n=Math.floor(n)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?l:n,s.cssMode?r[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-l:-n:s.virtualTranslate||(this.isHorizontal()?l-=this.cssOverflowAdjustment():n-=this.cssOverflowAdjustment(),r.style.transform=`translate3d(${l}px, ${n}px, 0px)`);let o=this.maxTranslate()-this.minTranslate();(0===o?0:(e-this.minTranslate())/o)!==a&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,s,r){let a;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);let l=this,{params:n,wrapperEl:d}=l;if(l.animating&&n.preventInteractionOnTransition)return!1;let p=l.minTranslate(),u=l.maxTranslate();if(a=s&&e>p?p:s&&e<u?u:e,l.updateProgress(a),n.cssMode){let e=l.isHorizontal();if(0===t)d[e?"scrollLeft":"scrollTop"]=-a;else{if(!l.support.smoothScroll)return(0,o.u)({swiper:l,targetPosition:-a,side:e?"left":"top"}),!0;d.scrollTo({[e?"left":"top"]:-a,behavior:"smooth"})}return!0}return 0===t?(l.setTransition(0),l.setTranslate(a),i&&(l.emit("beforeTransitionStart",t,r),l.emit("transitionEnd"))):(l.setTransition(t),l.setTranslate(a),i&&(l.emit("beforeTransitionStart",t,r),l.emit("transitionStart")),l.animating||(l.animating=!0,l.onTranslateToWrapperTransitionEnd||(l.onTranslateToWrapperTransitionEnd=function(e){l&&!l.destroyed&&e.target===this&&(l.wrapperEl.removeEventListener("transitionend",l.onTranslateToWrapperTransitionEnd),l.onTranslateToWrapperTransitionEnd=null,delete l.onTranslateToWrapperTransitionEnd,l.animating=!1,i&&l.emit("transitionEnd"))}),l.wrapperEl.addEventListener("transitionend",l.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:i}=this;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),v({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:i}=this;this.animating=!1,i.cssMode||(this.setTransition(0),v({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,s,r){let a;void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let l=this,n=e;n<0&&(n=0);let{params:d,snapGrid:p,slidesGrid:c,previousIndex:h,activeIndex:f,rtlTranslate:m,wrapperEl:g,enabled:v}=l;if(!v&&!s&&!r||l.destroyed||l.animating&&d.preventInteractionOnTransition)return!1;void 0===t&&(t=l.params.speed);let w=Math.min(l.params.slidesPerGroupSkip,n),b=w+Math.floor((n-w)/l.params.slidesPerGroup);b>=p.length&&(b=p.length-1);let y=-p[b];if(d.normalizeSlideIndex)for(let e=0;e<c.length;e+=1){let t=-Math.floor(100*y),i=Math.floor(100*c[e]),s=Math.floor(100*c[e+1]);void 0!==c[e+1]?t>=i&&t<s-(s-i)/2?n=e:t>=i&&t<s&&(n=e+1):t>=i&&(n=e)}if(l.initialized&&n!==f&&(!l.allowSlideNext&&(m?y>l.translate&&y>l.minTranslate():y<l.translate&&y<l.minTranslate())||!l.allowSlidePrev&&y>l.translate&&y>l.maxTranslate()&&(f||0)!==n))return!1;n!==(h||0)&&i&&l.emit("beforeSlideChangeStart"),l.updateProgress(y),a=n>f?"next":n<f?"prev":"reset";let S=l.virtual&&l.params.virtual.enabled;if(!(S&&r)&&(m&&-y===l.translate||!m&&y===l.translate))return l.updateActiveIndex(n),d.autoHeight&&l.updateAutoHeight(),l.updateSlidesClasses(),"slide"!==d.effect&&l.setTranslate(y),"reset"!==a&&(l.transitionStart(i,a),l.transitionEnd(i,a)),!1;if(d.cssMode){let e=l.isHorizontal(),i=m?y:-y;if(0===t)S&&(l.wrapperEl.style.scrollSnapType="none",l._immediateVirtual=!0),S&&!l._cssModeVirtualInitialSet&&l.params.initialSlide>0?(l._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{g[e?"scrollLeft":"scrollTop"]=i})):g[e?"scrollLeft":"scrollTop"]=i,S&&requestAnimationFrame(()=>{l.wrapperEl.style.scrollSnapType="",l._immediateVirtual=!1});else{if(!l.support.smoothScroll)return(0,o.u)({swiper:l,targetPosition:i,side:e?"left":"top"}),!0;g.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}let E=u().isSafari;return S&&!r&&E&&l.isElement&&l.virtual.update(!1,!1,n),l.setTransition(t),l.setTranslate(y),l.updateActiveIndex(n),l.updateSlidesClasses(),l.emit("beforeTransitionStart",t,s),l.transitionStart(i,a),0===t?l.transitionEnd(i,a):l.animating||(l.animating=!0,l.onSlideToWrapperTransitionEnd||(l.onSlideToWrapperTransitionEnd=function(e){l&&!l.destroyed&&e.target===this&&(l.wrapperEl.removeEventListener("transitionend",l.onSlideToWrapperTransitionEnd),l.onSlideToWrapperTransitionEnd=null,delete l.onSlideToWrapperTransitionEnd,l.transitionEnd(i,a))}),l.wrapperEl.addEventListener("transitionend",l.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,s){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);let a=r.grid&&r.params.grid&&r.params.grid.rows>1,l=e;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)l+=r.virtual.slidesBefore;else{let e;if(a){let t=l*r.params.grid.rows;e=r.slides.find(e=>+e.getAttribute("data-swiper-slide-index")===t).column}else e=r.getSlideIndexByData(l);let t=a?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:i}=r.params,n=r.params.slidesPerView;"auto"===n?n=r.slidesPerViewDynamic():(n=Math.ceil(parseFloat(r.params.slidesPerView,10)),i&&n%2==0&&(n+=1));let o=t-e<n;if(i&&(o=o||e<Math.ceil(n/2)),s&&i&&"auto"!==r.params.slidesPerView&&!a&&(o=!1),o){let s=i?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:s,slideTo:!0,activeSlideIndex:"next"===s?e+1:e-t+1,slideRealIndex:"next"===s?r.realIndex:void 0})}if(a){let e=l*r.params.grid.rows;l=r.slides.find(t=>+t.getAttribute("data-swiper-slide-index")===e).column}else l=r.getSlideIndexByData(l)}return requestAnimationFrame(()=>{r.slideTo(l,t,i,s)}),r},slideNext:function(e,t,i){void 0===t&&(t=!0);let s=this,{enabled:r,params:a,animating:l}=s;if(!r||s.destroyed)return s;void 0===e&&(e=s.params.speed);let n=a.slidesPerGroup;"auto"===a.slidesPerView&&1===a.slidesPerGroup&&a.slidesPerGroupAuto&&(n=Math.max(s.slidesPerViewDynamic("current",!0),1));let o=s.activeIndex<a.slidesPerGroupSkip?1:n,d=s.virtual&&a.virtual.enabled;if(a.loop){if(l&&!d&&a.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&a.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+o,e,t,i)}),!0}return a.rewind&&s.isEnd?s.slideTo(0,e,t,i):s.slideTo(s.activeIndex+o,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);let s=this,{params:r,snapGrid:a,slidesGrid:l,rtlTranslate:n,enabled:o,animating:d}=s;if(!o||s.destroyed)return s;void 0===e&&(e=s.params.speed);let p=s.virtual&&r.virtual.enabled;if(r.loop){if(d&&!p&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}function u(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let c=u(n?s.translate:-s.translate),h=a.map(e=>u(e)),f=r.freeMode&&r.freeMode.enabled,m=a[h.indexOf(c)-1];if(void 0===m&&(r.cssMode||f)){let e;a.forEach((t,i)=>{c>=t&&(e=i)}),void 0!==e&&(m=f?a[e]:a[e>0?e-1:e])}let g=0;if(void 0!==m&&((g=l.indexOf(m))<0&&(g=s.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(g=Math.max(g=g-s.slidesPerViewDynamic("previous",!0)+1,0))),r.rewind&&s.isBeginning){let r=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(r,e,t,i)}return r.loop&&0===s.activeIndex&&r.cssMode?(requestAnimationFrame(()=>{s.slideTo(g,e,t,i)}),!0):s.slideTo(g,e,t,i)},slideReset:function(e,t,i){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,s){if(void 0===t&&(t=!0),void 0===s&&(s=.5),this.destroyed)return;void 0===e&&(e=this.params.speed);let r=this.activeIndex,a=Math.min(this.params.slidesPerGroupSkip,r),l=a+Math.floor((r-a)/this.params.slidesPerGroup),n=this.rtlTranslate?this.translate:-this.translate;if(n>=this.snapGrid[l]){let e=this.snapGrid[l];n-e>(this.snapGrid[l+1]-e)*s&&(r+=this.params.slidesPerGroup)}else{let e=this.snapGrid[l-1];n-e<=(this.snapGrid[l]-e)*s&&(r-=this.params.slidesPerGroup)}return r=Math.min(r=Math.max(r,0),this.slidesGrid.length-1),this.slideTo(r,e,t,i)},slideToClickedSlide:function(){let e,t=this;if(t.destroyed)return;let{params:i,slidesEl:s}=t,r="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,a=t.getSlideIndexWhenGrid(t.clickedIndex),l=t.isElement?"swiper-slide":`.${i.slideClass}`,n=t.grid&&t.params.grid&&t.params.grid.rows>1;if(i.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),i.centeredSlides?t.slideToLoop(e):a>(n?(t.slides.length-r)/2-(t.params.grid.rows-1):t.slides.length-r)?(t.loopFix(),a=t.getSlideIndex((0,o.e)(s,`${l}[data-swiper-slide-index="${e}"]`)[0]),(0,o.n)(()=>{t.slideTo(a)})):t.slideTo(a)}else t.slideTo(a)}},loop:{loopCreate:function(e,t){let i=this,{params:s,slidesEl:r}=i;if(!s.loop||i.virtual&&i.params.virtual.enabled)return;let a=i.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||a)&&(()=>{let e=(0,o.e)(r,`.${s.slideBlankClass}`);e.forEach(e=>{e.remove()}),e.length>0&&(i.recalcSlides(),i.updateSlides())})();let l=s.slidesPerGroup*(a?s.grid.rows:1),n=i.slides.length%l!=0,d=a&&i.slides.length%s.grid.rows!=0,p=e=>{for(let t=0;t<e;t+=1){let e=i.isElement?(0,o.c)("swiper-slide",[s.slideBlankClass]):(0,o.c)("div",[s.slideClass,s.slideBlankClass]);i.slidesEl.append(e)}};n?s.loopAddBlankSlides?(p(l-i.slides.length%l),i.recalcSlides(),i.updateSlides()):(0,o.v)("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):d&&(s.loopAddBlankSlides?(p(s.grid.rows-i.slides.length%s.grid.rows),i.recalcSlides(),i.updateSlides()):(0,o.v)("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),(0,o.e)(r,`.${s.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}),i.loopFix({slideRealIndex:e,direction:s.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:s,setTranslate:r,activeSlideIndex:a,initial:l,byController:n,byMousewheel:d}=void 0===e?{}:e,p=this;if(!p.params.loop)return;p.emit("beforeLoopFix");let{slides:u,allowSlidePrev:c,allowSlideNext:h,slidesEl:f,params:m}=p,{centeredSlides:g,initialSlide:v}=m;if(p.allowSlidePrev=!0,p.allowSlideNext=!0,p.virtual&&m.virtual.enabled){i&&(m.centeredSlides||0!==p.snapIndex?m.centeredSlides&&p.snapIndex<m.slidesPerView?p.slideTo(p.virtual.slides.length+p.snapIndex,0,!1,!0):p.snapIndex===p.snapGrid.length-1&&p.slideTo(p.virtual.slidesBefore,0,!1,!0):p.slideTo(p.virtual.slides.length,0,!1,!0)),p.allowSlidePrev=c,p.allowSlideNext=h,p.emit("loopFix");return}let w=m.slidesPerView;"auto"===w?w=p.slidesPerViewDynamic():(w=Math.ceil(parseFloat(m.slidesPerView,10)),g&&w%2==0&&(w+=1));let b=m.slidesPerGroupAuto?w:m.slidesPerGroup,y=g?Math.max(b,Math.ceil(w/2)):b;y%b!=0&&(y+=b-y%b),p.loopedSlides=y+=m.loopAdditionalSlides;let S=p.grid&&m.grid&&m.grid.rows>1;u.length<w+y||"cards"===p.params.effect&&u.length<w+2*y?(0,o.v)("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):S&&"row"===m.grid.fill&&(0,o.v)("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let E=[],T=[],x=S?Math.ceil(u.length/m.grid.rows):u.length,C=l&&x-v<w&&!g,M=C?v:p.activeIndex;void 0===a?a=p.getSlideIndex(u.find(e=>e.classList.contains(m.slideActiveClass))):M=a;let P="next"===s||!s,L="prev"===s||!s,k=0,O=0,_=(S?u[a].column:a)+(g&&void 0===r?-w/2+.5:0);if(_<y){k=Math.max(y-_,b);for(let e=0;e<y-_;e+=1){let t=e-Math.floor(e/x)*x;if(S){let e=x-t-1;for(let t=u.length-1;t>=0;t-=1)u[t].column===e&&E.push(t)}else E.push(x-t-1)}}else if(_+w>x-y){O=Math.max(_-(x-2*y),b),C&&(O=Math.max(O,w-x+v+1));for(let e=0;e<O;e+=1){let t=e-Math.floor(e/x)*x;S?u.forEach((e,i)=>{e.column===t&&T.push(i)}):T.push(t)}}if(p.__preventObserver__=!0,requestAnimationFrame(()=>{p.__preventObserver__=!1}),"cards"===p.params.effect&&u.length<w+2*y&&(T.includes(a)&&T.splice(T.indexOf(a),1),E.includes(a)&&E.splice(E.indexOf(a),1)),L&&E.forEach(e=>{u[e].swiperLoopMoveDOM=!0,f.prepend(u[e]),u[e].swiperLoopMoveDOM=!1}),P&&T.forEach(e=>{u[e].swiperLoopMoveDOM=!0,f.append(u[e]),u[e].swiperLoopMoveDOM=!1}),p.recalcSlides(),"auto"===m.slidesPerView?p.updateSlides():S&&(E.length>0&&L||T.length>0&&P)&&p.slides.forEach((e,t)=>{p.grid.updateSlide(t,e,p.slides)}),m.watchSlidesProgress&&p.updateSlidesOffset(),i){if(E.length>0&&L){if(void 0===t){let e=p.slidesGrid[M],t=p.slidesGrid[M+k]-e;d?p.setTranslate(p.translate-t):(p.slideTo(M+Math.ceil(k),0,!1,!0),r&&(p.touchEventsData.startTranslate=p.touchEventsData.startTranslate-t,p.touchEventsData.currentTranslate=p.touchEventsData.currentTranslate-t))}else if(r){let e=S?E.length/m.grid.rows:E.length;p.slideTo(p.activeIndex+e,0,!1,!0),p.touchEventsData.currentTranslate=p.translate}}else if(T.length>0&&P)if(void 0===t){let e=p.slidesGrid[M],t=p.slidesGrid[M-O]-e;d?p.setTranslate(p.translate-t):(p.slideTo(M-O,0,!1,!0),r&&(p.touchEventsData.startTranslate=p.touchEventsData.startTranslate-t,p.touchEventsData.currentTranslate=p.touchEventsData.currentTranslate-t))}else{let e=S?T.length/m.grid.rows:T.length;p.slideTo(p.activeIndex-e,0,!1,!0)}}if(p.allowSlidePrev=c,p.allowSlideNext=h,p.controller&&p.controller.control&&!n){let e={slideRealIndex:t,direction:s,setTranslate:r,activeSlideIndex:a,byController:!0};Array.isArray(p.controller.control)?p.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===m.slidesPerView&&i})}):p.controller.control instanceof p.constructor&&p.controller.control.params.loop&&p.controller.control.loopFix({...e,slideTo:p.controller.control.params.slidesPerView===m.slidesPerView&&i})}p.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||!t||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let i=[];this.slides.forEach(e=>{i[void 0===e.swiperSlideIndex?+e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),i.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let{params:e}=this;this.onTouchStart=b.bind(this),this.onTouchMove=y.bind(this),this.onTouchEnd=S.bind(this),this.onDocumentTouchStart=M.bind(this),e.cssMode&&(this.onScroll=x.bind(this)),this.onClick=T.bind(this),this.onLoad=C.bind(this),P(this,"on")},detachEvents:function(){P(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:i,params:s,el:r}=e,a=s.breakpoints;if(!a||a&&0===Object.keys(a).length)return;let l=(0,n.g)(),d="window"!==s.breakpointsBase&&s.breakpointsBase?"container":s.breakpointsBase,p=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?e.el:l.querySelector(s.breakpointsBase),u=e.getBreakpoint(a,d,p);if(!u||e.currentBreakpoint===u)return;let c=(u in a?a[u]:void 0)||e.originalParams,h=L(e,s),f=L(e,c),m=e.params.grabCursor,g=c.grabCursor,v=s.enabled;h&&!f?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!h&&f&&(r.classList.add(`${s.containerModifierClass}grid`),(c.grid.fill&&"column"===c.grid.fill||!c.grid.fill&&"column"===s.grid.fill)&&r.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),m&&!g?e.unsetGrabCursor():!m&&g&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===c[t])return;let i=s[t]&&s[t].enabled,r=c[t]&&c[t].enabled;i&&!r&&e[t].disable(),!i&&r&&e[t].enable()});let w=c.direction&&c.direction!==s.direction,b=s.loop&&(c.slidesPerView!==s.slidesPerView||w),y=s.loop;w&&i&&e.changeDirection(),(0,o.x)(e.params,c);let S=e.params.enabled,E=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),v&&!S?e.disable():!v&&S&&e.enable(),e.currentBreakpoint=u,e.emit("_beforeBreakpoint",c),i&&(b?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!y&&E?(e.loopCreate(t),e.updateSlides()):y&&!E&&e.loopDestroy()),e.emit("breakpoint",c)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let s=!1,r=(0,n.a)(),a="window"===t?r.innerHeight:i.clientHeight,l=Object.keys(e).map(e=>"string"==typeof e&&0===e.indexOf("@")?{value:a*parseFloat(e.substr(1)),point:e}:{value:e,point:e});l.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<l.length;e+=1){let{point:a,value:n}=l[e];"window"===t?r.matchMedia(`(min-width: ${n}px)`).matches&&(s=a):n<=i.clientWidth&&(s=a)}return s||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:i}=t;if(i){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*i;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:i,el:s,device:r}=this,a=function(e,t){let i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(s=>{e[s]&&i.push(t+s)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...a),s.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},_={};class I{constructor(){let e,t;for(var i=arguments.length,s=Array(i),r=0;r<i;r++)s[r]=arguments[r];1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?t=s[0]:[e,t]=s,t||(t={}),t=(0,o.x)({},t),e&&!t.el&&(t.el=e);let a=(0,n.g)();if(t.el&&"string"==typeof t.el&&a.querySelectorAll(t.el).length>1){let e=[];return a.querySelectorAll(t.el).forEach(i=>{let s=(0,o.x)({},t,{el:i});e.push(new I(s))}),e}let l=this;l.__swiper__=!0,l.support=d(),l.device=p({userAgent:t.userAgent}),l.browser=u(),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],t.modules&&Array.isArray(t.modules)&&l.modules.push(...t.modules);let c={};l.modules.forEach(e=>{e({params:t,swiper:l,extendParams:function(e,t){return function(i){void 0===i&&(i={});let s=Object.keys(i)[0],r=i[s];return"object"!=typeof r||null===r?void(0,o.x)(t,i):(!0===e[s]&&(e[s]={enabled:!0}),"navigation"===s&&e[s]&&e[s].enabled&&!e[s].prevEl&&!e[s].nextEl&&(e[s].auto=!0),["pagination","scrollbar"].indexOf(s)>=0&&e[s]&&e[s].enabled&&!e[s].el&&(e[s].auto=!0),s in e&&"enabled"in r)?void("object"==typeof e[s]&&!("enabled"in e[s])&&(e[s].enabled=!0),!e[s]&&(e[s]={enabled:!1}),(0,o.x)(t,i)):void(0,o.x)(t,i)}}(t,c),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});let h=(0,o.x)({},k,c);return l.params=(0,o.x)({},h,_,t),l.originalParams=(0,o.x)({},l.params),l.passedParams=(0,o.x)({},t),l.params&&l.params.on&&Object.keys(l.params.on).forEach(e=>{l.on(e,l.params.on[e])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),Object.assign(l,{enabled:l.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===l.params.direction,isVertical:()=>"vertical"===l.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:i}=this,s=(0,o.e)(t,`.${i.slideClass}, swiper-slide`),r=(0,o.i)(s[0]);return(0,o.i)(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>+t.getAttribute("data-swiper-slide-index")===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&("column"===this.params.grid.fill?e=Math.floor(e/this.params.grid.rows):"row"===this.params.grid.fill&&(e%=Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=(0,o.e)(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),s=(this.maxTranslate()-i)*e+i;this.translateTo(s,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(i=>{let s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:i,slides:s,slidesGrid:r,slidesSizesGrid:a,size:l,activeIndex:n}=this,o=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=s[n]?Math.ceil(s[n].swiperSlideSize):0;for(let i=n+1;i<s.length;i+=1)s[i]&&!e&&(t+=Math.ceil(s[i].swiperSlideSize),o+=1,t>l&&(e=!0));for(let i=n-1;i>=0;i-=1)s[i]&&!e&&(t+=s[i].swiperSlideSize,o+=1,t>l&&(e=!0))}else if("current"===e)for(let e=n+1;e<s.length;e+=1)(t?r[e]+a[e]-r[n]<l:r[e]-r[n]<l)&&(o+=1);else for(let e=n-1;e>=0;e-=1)r[n]-r[e]<l&&(o+=1);return o}update(){let e,t=this;if(!t||t.destroyed)return;let{snapGrid:i,params:s}=t;function r(){let e=Math.min(Math.max(t.rtlTranslate?-1*t.translate:t.translate,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}if(s.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&f(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)r(),s.autoHeight&&t.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&t.isEnd&&!s.centeredSlides){let i=t.virtual&&s.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(i.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||r()}s.watchOverflow&&i!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let i=this.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${i}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let s=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,r=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(s()):(0,o.e)(i,s())[0];return!r&&t.params.createElements&&(r=(0,o.c)("div",t.params.wrapperClass),i.append(r),(0,o.e)(i,`.${t.params.slideClass}`).forEach(e=>{r.append(e)})),Object.assign(t,{el:i,wrapperEl:r,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:r,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===(0,o.q)(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===(0,o.q)(i,"direction")),wrongRTL:"-webkit-box"===(0,o.q)(r,"display")}),!0}init(e){let t=this;if(t.initialized||!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();let i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(e=>{e.complete?f(t,e):e.addEventListener("load",e=>{f(t,e.target)})}),g(t),t.initialized=!0,g(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let i=this,{params:s,el:r,wrapperEl:a,slides:l}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),a&&a.removeAttribute("style"),l&&l.length&&l.forEach(e=>{e.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),(0,o.y)(i)),i.destroyed=!0),null}static extendDefaults(e){(0,o.x)(_,e)}static get extendedDefaults(){return _}static get defaults(){return k}static installModule(e){I.prototype.__modules__||(I.prototype.__modules__=[]);let t=I.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>I.installModule(e)):I.installModule(e),I}}Object.keys(O).forEach(e=>{Object.keys(O[e]).forEach(t=>{I.prototype[t]=O[e][t]})}),I.use([function(e){let{swiper:t,on:i,emit:s}=e,r=(0,n.a)(),a=null,l=null,o=()=>{t&&!t.destroyed&&t.initialized&&(s("beforeResize"),s("resize"))},d=()=>{t&&!t.destroyed&&t.initialized&&s("orientationchange")};i("init",()=>{if(t.params.resizeObserver&&void 0!==r.ResizeObserver)return void(t&&!t.destroyed&&t.initialized&&(a=new ResizeObserver(e=>{l=r.requestAnimationFrame(()=>{let{width:i,height:s}=t,r=i,a=s;e.forEach(e=>{let{contentBoxSize:i,contentRect:s,target:l}=e;l&&l!==t.el||(r=s?s.width:(i[0]||i).inlineSize,a=s?s.height:(i[0]||i).blockSize)}),(r!==i||a!==s)&&o()})})).observe(t.el));r.addEventListener("resize",o),r.addEventListener("orientationchange",d)}),i("destroy",()=>{l&&r.cancelAnimationFrame(l),a&&a.unobserve&&t.el&&(a.unobserve(t.el),a=null),r.removeEventListener("resize",o),r.removeEventListener("orientationchange",d)})},function(e){let{swiper:t,extendParams:i,on:s,emit:r}=e,a=[],l=(0,n.a)(),d=function(e,i){void 0===i&&(i={});let s=new(l.MutationObserver||l.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length)return void r("observerUpdate",e[0]);let i=function(){r("observerUpdate",e[0])};l.requestAnimationFrame?l.requestAnimationFrame(i):l.setTimeout(i,0)});s.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:t.isElement||(void 0===i.childList||i).childList,characterData:void 0===i.characterData||i.characterData}),a.push(s)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=(0,o.b)(t.hostEl);for(let t=0;t<e.length;t+=1)d(e[t])}d(t.hostEl,{childList:t.params.observeSlideChildren}),d(t.wrapperEl,{attributes:!1})}}),s("destroy",()=>{a.forEach(e=>{e.disconnect()}),a.splice(0,a.length)})}]);let A=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function z(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function $(e,t){let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:z(t[i])&&z(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:$(e[i],t[i]):e[i]=t[i]})}function D(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function j(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function G(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function N(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),i=[];return t.forEach(e=>{0>i.indexOf(e)&&i.push(e)}),i.join(" ")}function B(){return(B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(this,arguments)}function F(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function V(e,t){return"undefined"==typeof window?(0,l.useEffect)(e,t):(0,l.useLayoutEffect)(e,t)}let R=(0,l.createContext)(null),q=(0,l.createContext)(null),H=(0,l.forwardRef)(function(e,t){var i;let{className:s,tag:r="div",wrapperTag:a="div",children:n,onSwiper:d,...p}=void 0===e?{}:e,u=!1,[c,h]=(0,l.useState)("swiper"),[f,m]=(0,l.useState)(null),[g,v]=(0,l.useState)(!1),w=(0,l.useRef)(!1),b=(0,l.useRef)(null),y=(0,l.useRef)(null),S=(0,l.useRef)(null),E=(0,l.useRef)(null),T=(0,l.useRef)(null),x=(0,l.useRef)(null),C=(0,l.useRef)(null),M=(0,l.useRef)(null),{params:P,passedParams:L,rest:O,events:_}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let i={on:{}},s={},r={};$(i,k),i._emitClasses=!0,i.init=!1;let a={},l=A.map(e=>e.replace(/_/,""));return Object.keys(Object.assign({},e)).forEach(n=>{void 0!==e[n]&&(l.indexOf(n)>=0?z(e[n])?(i[n]={},r[n]={},$(i[n],e[n]),$(r[n],e[n])):(i[n]=e[n],r[n]=e[n]):0===n.search(/on[A-Z]/)&&"function"==typeof e[n]?t?s[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:i.on[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:a[n]=e[n])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]}),{params:i,passedParams:r,rest:a,events:s}}(p),{slides:R,slots:H}=function(e){let t=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return l.Children.toArray(e).forEach(e=>{if(F(e))t.push(e);else if(e.props&&e.props.slot&&i[e.props.slot])i[e.props.slot].push(e);else if(e.props&&e.props.children){let s=function e(t){let i=[];return l.Children.toArray(t).forEach(t=>{F(t)?i.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>i.push(e))}),i}(e.props.children);s.length>0?s.forEach(e=>t.push(e)):i["container-end"].push(e)}else i["container-end"].push(e)}),{slides:t,slots:i}}(n),W=()=>{v(!g)};Object.assign(P.on,{_containerClasses(e,t){h(t)}});let X=()=>{Object.assign(P.on,_),u=!0;let e={...P};if(delete e.wrapperClass,y.current=new I(e),y.current.virtual&&y.current.params.virtual.enabled){y.current.virtual.slides=R;let e={cache:!1,slides:R,renderExternal:m,renderExternalUpdate:!1};$(y.current.params.virtual,e),$(y.current.originalParams.virtual,e)}};return b.current||X(),y.current&&y.current.on("_beforeBreakpoint",W),(0,l.useEffect)(()=>()=>{y.current&&y.current.off("_beforeBreakpoint",W)}),(0,l.useEffect)(()=>{!w.current&&y.current&&(y.current.emitSlidesClasses(),w.current=!0)}),V(()=>{if(t&&(t.current=b.current),b.current)return y.current.destroyed&&X(),!function(e,t){let{el:i,nextEl:s,prevEl:r,paginationEl:a,scrollbarEl:l,swiper:n}=e;D(t)&&s&&r&&(n.params.navigation.nextEl=s,n.originalParams.navigation.nextEl=s,n.params.navigation.prevEl=r,n.originalParams.navigation.prevEl=r),j(t)&&a&&(n.params.pagination.el=a,n.originalParams.pagination.el=a),G(t)&&l&&(n.params.scrollbar.el=l,n.originalParams.scrollbar.el=l),n.init(i)}({el:b.current,nextEl:T.current,prevEl:x.current,paginationEl:C.current,scrollbarEl:M.current,swiper:y.current},P),d&&!y.current.destroyed&&d(y.current),()=>{y.current&&!y.current.destroyed&&y.current.destroy(!0,!1)}},[]),V(()=>{!u&&_&&y.current&&Object.keys(_).forEach(e=>{y.current.on(e,_[e])});let e=function(e,t,i,s,r){let a=[];if(!t)return a;let l=e=>{0>a.indexOf(e)&&a.push(e)};if(i&&s){let e=s.map(r),t=i.map(r);e.join("")!==t.join("")&&l("children"),s.length!==i.length&&l("children")}return A.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(i=>{if(i in e&&i in t)if(z(e[i])&&z(t[i])){let s=Object.keys(e[i]),r=Object.keys(t[i]);s.length!==r.length?l(i):(s.forEach(s=>{e[i][s]!==t[i][s]&&l(i)}),r.forEach(s=>{e[i][s]!==t[i][s]&&l(i)}))}else e[i]!==t[i]&&l(i)}),a}(L,S.current,R,E.current,e=>e.key);return S.current=L,E.current=R,e.length&&y.current&&!y.current.destroyed&&function(e){let t,i,s,r,a,l,n,d,{swiper:p,slides:u,passedParams:c,changedParams:h,nextEl:f,prevEl:m,scrollbarEl:g,paginationEl:v}=e,w=h.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:b,pagination:y,navigation:S,scrollbar:E,virtual:T,thumbs:x}=p;h.includes("thumbs")&&c.thumbs&&c.thumbs.swiper&&!c.thumbs.swiper.destroyed&&b.thumbs&&(!b.thumbs.swiper||b.thumbs.swiper.destroyed)&&(t=!0),h.includes("controller")&&c.controller&&c.controller.control&&b.controller&&!b.controller.control&&(i=!0),h.includes("pagination")&&c.pagination&&(c.pagination.el||v)&&(b.pagination||!1===b.pagination)&&y&&!y.el&&(s=!0),h.includes("scrollbar")&&c.scrollbar&&(c.scrollbar.el||g)&&(b.scrollbar||!1===b.scrollbar)&&E&&!E.el&&(r=!0),h.includes("navigation")&&c.navigation&&(c.navigation.prevEl||m)&&(c.navigation.nextEl||f)&&(b.navigation||!1===b.navigation)&&S&&!S.prevEl&&!S.nextEl&&(a=!0);let C=e=>{p[e]&&(p[e].destroy(),"navigation"===e?(p.isElement&&(p[e].prevEl.remove(),p[e].nextEl.remove()),b[e].prevEl=void 0,b[e].nextEl=void 0,p[e].prevEl=void 0,p[e].nextEl=void 0):(p.isElement&&p[e].el.remove(),b[e].el=void 0,p[e].el=void 0))};h.includes("loop")&&p.isElement&&(b.loop&&!c.loop?l=!0:!b.loop&&c.loop?n=!0:d=!0),w.forEach(e=>{if(z(b[e])&&z(c[e]))Object.assign(b[e],c[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in c[e]&&!c[e].enabled&&C(e);else{let t=c[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&C(e):b[e]=c[e]}}),w.includes("controller")&&!i&&p.controller&&p.controller.control&&b.controller&&b.controller.control&&(p.controller.control=b.controller.control),h.includes("children")&&u&&T&&b.virtual.enabled?(T.slides=u,T.update(!0)):h.includes("virtual")&&T&&b.virtual.enabled&&(u&&(T.slides=u),T.update(!0)),h.includes("children")&&u&&b.loop&&(d=!0),t&&x.init()&&x.update(!0),i&&(p.controller.control=b.controller.control),s&&(p.isElement&&(!v||"string"==typeof v)&&((v=document.createElement("div")).classList.add("swiper-pagination"),v.part.add("pagination"),p.el.appendChild(v)),v&&(b.pagination.el=v),y.init(),y.render(),y.update()),r&&(p.isElement&&(!g||"string"==typeof g)&&((g=document.createElement("div")).classList.add("swiper-scrollbar"),g.part.add("scrollbar"),p.el.appendChild(g)),g&&(b.scrollbar.el=g),E.init(),E.updateSize(),E.setTranslate()),a&&(p.isElement&&(f&&"string"!=typeof f||((f=document.createElement("div")).classList.add("swiper-button-next"),(0,o.s)(f,p.hostEl.constructor.nextButtonSvg),f.part.add("button-next"),p.el.appendChild(f)),m&&"string"!=typeof m||((m=document.createElement("div")).classList.add("swiper-button-prev"),(0,o.s)(m,p.hostEl.constructor.prevButtonSvg),m.part.add("button-prev"),p.el.appendChild(m))),f&&(b.navigation.nextEl=f),m&&(b.navigation.prevEl=m),S.init(),S.update()),h.includes("allowSlideNext")&&(p.allowSlideNext=c.allowSlideNext),h.includes("allowSlidePrev")&&(p.allowSlidePrev=c.allowSlidePrev),h.includes("direction")&&p.changeDirection(c.direction,!1),(l||d)&&p.loopDestroy(),(n||d)&&p.loopCreate(),p.update()}({swiper:y.current,slides:R,passedParams:L,changedParams:e,nextEl:T.current,prevEl:x.current,scrollbarEl:M.current,paginationEl:C.current}),()=>{_&&y.current&&Object.keys(_).forEach(e=>{y.current.off(e,_[e])})}}),V(()=>{var e;(e=y.current)&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())},[f]),l.createElement(r,B({ref:b,className:N(`${c}${s?` ${s}`:""}`)},O),l.createElement(q.Provider,{value:y.current},H["container-start"],l.createElement(a,{className:(void 0===(i=P.wrapperClass)&&(i=""),i)?i.includes("swiper-wrapper")?i:`swiper-wrapper ${i}`:"swiper-wrapper"},H["wrapper-start"],P.virtual?function(e,t,i){if(!i)return null;let s=e=>{let i=e;return e<0?i=t.length+e:i>=t.length&&(i-=t.length),i},r=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:a,to:n}=i,o=e.params.loop?-t.length:0,d=e.params.loop?2*t.length:t.length,p=[];for(let e=o;e<d;e+=1)e>=a&&e<=n&&p.push(t[s(e)]);return p.map((t,i)=>l.cloneElement(t,{swiper:e,style:r,key:t.props.virtualIndex||t.key||`slide-${i}`}))}(y.current,R,f):R.map((e,t)=>l.cloneElement(e,{swiper:y.current,swiperSlideIndex:t})),H["wrapper-end"]),D(P)&&l.createElement(l.Fragment,null,l.createElement("div",{ref:x,className:"swiper-button-prev"}),l.createElement("div",{ref:T,className:"swiper-button-next"})),G(P)&&l.createElement("div",{ref:M,className:"swiper-scrollbar"}),j(P)&&l.createElement("div",{ref:C,className:"swiper-pagination"}),H["container-end"]))});H.displayName="Swiper";let W=(0,l.forwardRef)(function(e,t){let{tag:i="div",children:s,className:r="",swiper:a,zoom:n,lazy:o,virtualIndex:d,swiperSlideIndex:p,...u}=void 0===e?{}:e,c=(0,l.useRef)(null),[h,f]=(0,l.useState)("swiper-slide"),[m,g]=(0,l.useState)(!1);function v(e,t,i){t===c.current&&f(i)}V(()=>{if(void 0!==p&&(c.current.swiperSlideIndex=p),t&&(t.current=c.current),c.current&&a){if(a.destroyed){"swiper-slide"!==h&&f("swiper-slide");return}return a.on("_slideClass",v),()=>{a&&a.off("_slideClass",v)}}}),V(()=>{a&&c.current&&!a.destroyed&&f(a.getSlideClasses(c.current))},[a]);let w={isActive:h.indexOf("swiper-slide-active")>=0,isVisible:h.indexOf("swiper-slide-visible")>=0,isPrev:h.indexOf("swiper-slide-prev")>=0,isNext:h.indexOf("swiper-slide-next")>=0},b=()=>"function"==typeof s?s(w):s;return l.createElement(i,B({ref:c,className:N(`${h}${r?` ${r}`:""}`),"data-swiper-slide-index":d,onLoad:()=>{g(!0)}},u),n&&l.createElement(R.Provider,{value:w},l.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof n?n:void 0},b(),o&&!m&&l.createElement("div",{className:"swiper-lazy-preloader"}))),!n&&l.createElement(R.Provider,{value:w},b(),o&&!m&&l.createElement("div",{className:"swiper-lazy-preloader"})))});W.displayName="SwiperSlide"},8230:(e,t,i)=>{e.exports=i(1639)},8940:(e,t,i)=>{"use strict";function s(e,t,i,s){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return s}}),i(7810),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8953:()=>{},9099:(e,t,i)=>{e.exports=i(8253)}}]);