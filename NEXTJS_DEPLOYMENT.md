# دليل نشر Next.js - خبرة المطابخ
# Next.js Deployment Guide - Khobra Kitchens

## 📋 نظرة عامة

تم تحويل المشروع بنجاح من React/Vite إلى Next.js 15 مع الحفاظ على جميع الوظائف والمميزات.

## 🔄 التغييرات الرئيسية

### 1. البنية الجديدة
- **Framework**: Next.js 15 بدلاً من React/Vite
- **Routing**: Next.js Pages Router بدلاً من React Router
- **SEO**: Next.js Head بدلاً من React Helmet
- **Build**: Next.js build system بدلاً من Vite

### 2. الملفات المحدثة
- `package.json` - تبعيات Next.js الجديدة
- `next.config.js` - إعدادات Next.js
- `pages/` - صفحات Next.js
- `pages/_app.js` - تطبيق Next.js الرئيسي
- `pages/_document.js` - HTML الأساسي
- `.eslintrc.json` - إعدادات ESLint لـ Next.js
- `postcss.config.js` - إعدادات PostCSS محدثة

### 3. المكونات المحدثة
- `src/components/Navbar.jsx` - استخدام Next.js Router
- `src/components/SEO.jsx` - استخدام Next.js Head
- `src/config/api.js` - دعم SSR
- `src/config/env.js` - متغيرات البيئة لـ Next.js

## 🚀 خطوات النشر

### 1. تحديث الخادم

```bash
# الانتقال إلى مجلد المشروع
cd /var/www/html

# تثبيت التبعيات الجديدة
npm install --legacy-peer-deps

# بناء المشروع
npm run build
```

### 2. تحديث PM2

```bash
# إيقاف العمليات الحالية
pm2 stop all

# بدء العمليات الجديدة
pm2 start ecosystem.config.cjs --env production

# حفظ الإعدادات
pm2 save
```

### 3. تحديث Nginx

```bash
# نسخ إعدادات Nginx الجديدة
sudo cp khobrakitchens-nextjs.conf /etc/nginx/sites-available/khobrakitchens.com

# اختبار الإعدادات
sudo nginx -t

# إعادة تحميل Nginx
sudo systemctl reload nginx
```

## 🔧 إعدادات الخادم

### PM2 Configuration
- **API Server**: المنفذ 3002 (Express.js)
- **Next.js Server**: المنفذ 3000
- **Nginx**: المنفذ 80/443

### Nginx Proxy
- `/api/*` → `http://127.0.0.1:3002` (API Server)
- `/*` → `http://127.0.0.1:3000` (Next.js Server)

## 📊 الأداء والتحسينات

### مميزات Next.js
- **Server-Side Rendering (SSR)**: تحسين SEO
- **Static Generation**: صفحات سريعة
- **Image Optimization**: تحسين الصور تلقائياً
- **Code Splitting**: تحميل أسرع
- **Built-in SEO**: دعم محسن لمحركات البحث

### التحسينات المطبقة
- ✅ تحويل React Router إلى Next.js Router
- ✅ تحويل React Helmet إلى Next.js Head
- ✅ دعم SSR للمتغيرات البيئية
- ✅ تحسين إعدادات PostCSS
- ✅ تحديث إعدادات ESLint

## 🧪 الاختبار

### التحقق من التشغيل
```bash
# اختبار Next.js
curl http://localhost:3000

# اختبار API
curl http://localhost:3002/api/hero

# اختبار Nginx
curl https://khobrakitchens.com
```

### مراقبة السجلات
```bash
# سجلات PM2
pm2 logs

# سجلات Nginx
sudo tail -f /var/log/nginx/khobrakitchens.error.log
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ 502 Bad Gateway**: تحقق من تشغيل Next.js على المنفذ 3000
2. **خطأ API 404**: تحقق من تشغيل Express على المنفذ 3002
3. **مشاكل CSS**: تحقق من إعدادات PostCSS

### الحلول
```bash
# إعادة تشغيل الخدمات
pm2 restart all

# إعادة بناء المشروع
npm run build

# تحديث التبعيات
npm install --legacy-peer-deps
```

## 📝 ملاحظات مهمة

1. **متغيرات البيئة**: تم تحديثها لاستخدام `NEXT_PUBLIC_` prefix
2. **Static Files**: الملفات الثابتة في مجلد `public/`
3. **API Integration**: الـ API الخارجي يعمل بشكل منفصل
4. **Database**: SQLite يعمل على جانب العميل

## 🎯 الخطوات التالية

- [ ] اختبار جميع الصفحات والوظائف
- [ ] تحسين الصور باستخدام Next.js Image
- [ ] إضافة المزيد من تحسينات SEO
- [ ] مراقبة الأداء والسرعة

---

**تاريخ التحديث**: 2025-07-16  
**المطور**: Augment Agent  
**الحالة**: مكتمل ✅
