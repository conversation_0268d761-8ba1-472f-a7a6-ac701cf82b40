# دليل النشر - خ<PERSON>رة المطابخ
## Khobra Kitchens Deployment Guide

### معلومات المشروع
- **الدومين**: khobrakitchens.com
- **عنوان IP للسرفر**: ************
- **التقنيات المستخدمة**: React, Vite, Nginx
- **نوع المشروع**: Single Page Application (SPA)

---

## 🚀 خطوات النشر السريع

### 1. التحضير الأولي

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Node.js و npm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# تثبيت Nginx
sudo apt install nginx -y

# تثبيت Certbot للـ SSL
sudo apt install certbot python3-certbot-nginx -y
```

### 2. رفع الملفات

```bash
# نسخ ملفات المشروع إلى السرفر
scp -r * user@************:/var/www/html/

# أو استخدام Git
git clone https://github.com/your-repo/khobra-kitchens.git /var/www/html/
```

### 3. تشغيل سكريبت النشر

```bash
cd /var/www/html
chmod +x deploy.sh
./deploy.sh
```

---

## 🔧 النشر اليدوي

### 1. تثبيت التبعيات

```bash
cd /var/www/html
npm install --legacy-peer-deps
```

### 2. بناء المشروع

```bash
npm run build
```

### 3. إعداد Nginx

```bash
# نسخ إعدادات Nginx
sudo cp nginx.conf /etc/nginx/sites-available/khobrakitchens.com
sudo ln -s /etc/nginx/sites-available/khobrakitchens.com /etc/nginx/sites-enabled/

# اختبار الإعدادات
sudo nginx -t

# إعادة تشغيل Nginx
sudo systemctl restart nginx
```

### 4. إعداد SSL

```bash
# الحصول على شهادة SSL مجانية
sudo certbot --nginx -d khobrakitchens.com -d www.khobrakitchens.com

# تجديد تلقائي للشهادة
sudo crontab -e
# إضافة السطر التالي:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 🐳 النشر باستخدام Docker

### 1. بناء الصورة

```bash
docker build -t khobra-kitchens .
```

### 2. تشغيل الحاوية

```bash
docker run -d \
  --name khobra-kitchens-web \
  -p 80:80 \
  -p 443:443 \
  khobra-kitchens
```

### 3. استخدام Docker Compose

```bash
# تشغيل الخدمة الأساسية فقط
docker-compose up -d khobra-kitchens

# تشغيل جميع الخدمات (مع قاعدة البيانات والمراقبة)
docker-compose --profile database --profile monitoring up -d
```

---

## 🔐 الأمان والحماية

### 1. بيانات الدخول الافتراضية

```
اسم المستخدم: admin_khobra_kitchens
كلمة المرور: khobra_admin_2024
البريد الإلكتروني: <EMAIL>
```

**⚠️ مهم**: يجب تغيير هذه البيانات فور النشر!

### 2. تغيير بيانات الدخول

1. افتح ملف `.env`
2. قم بتشفير اسم المستخدم وكلمة المرور الجديدة باستخدام base64
3. حدث المتغيرات:
   ```
   VITE_ADMIN_USERNAME_HASH="new_encoded_username"
   VITE_ADMIN_PASSWORD_HASH="new_encoded_password"
   ```
4. أعد بناء المشروع: `npm run build`

### 3. إعدادات الأمان الإضافية

```bash
# تقييد الوصول لوحة الإدارة حسب IP
# في ملف nginx.conf، قم بإلغاء التعليق عن:
# location /admin {
#     allow ************;
#     allow 127.0.0.1;
#     deny all;
# }

# إعداد جدار الحماية
sudo ufw enable
sudo ufw allow 'Nginx Full'
sudo ufw allow ssh
```

---

## 📊 المراقبة والصيانة

### 1. فحص حالة الخدمات

```bash
# حالة Nginx
sudo systemctl status nginx

# سجلات Nginx
sudo tail -f /var/log/nginx/khobrakitchens.com.access.log
sudo tail -f /var/log/nginx/khobrakitchens.com.error.log

# حالة SSL
sudo certbot certificates
```

### 2. النسخ الاحتياطي

```bash
# نسخ احتياطي للملفات
sudo cp -r /var/www/html/dist /var/backups/khobrakitchens/dist_$(date +%Y%m%d)

# نسخ احتياطي لإعدادات Nginx
sudo cp /etc/nginx/sites-available/khobrakitchens.com /var/backups/nginx/
```

### 3. التحديثات

```bash
# تحديث التبعيات
npm update

# إعادة البناء
npm run build

# إعادة تشغيل الخدمات
sudo systemctl reload nginx
```

---

## 🌐 إعدادات DNS

### سجلات DNS المطلوبة:

```
Type    Name                Value           TTL
A       khobrakitchens.com  ************    3600
A       www                 ************    3600
CNAME   admin               khobrakitchens.com  3600
```

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **خطأ 404 عند التنقل**
   - تأكد من إعدادات React Router في nginx.conf
   - تحقق من وجود `try_files $uri $uri/ /index.html;`

2. **مشاكل SSL**
   ```bash
   sudo certbot renew --dry-run
   sudo nginx -t
   ```

3. **مشاكل الأذونات**
   ```bash
   sudo chown -R www-data:www-data /var/www/html/dist
   sudo chmod -R 755 /var/www/html/dist
   ```

4. **مشاكل البناء**
   ```bash
   rm -rf node_modules package-lock.json
   npm install --legacy-peer-deps
   npm run build
   ```

---

## 📞 الدعم والمساعدة

- **الموقع**: https://khobrakitchens.com
- **لوحة الإدارة**: https://khobrakitchens.com/admin
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966123456789

---

## 📝 ملاحظات مهمة

1. تأكد من تحديث DNS للإشارة إلى عنوان IP الصحيح
2. قم بتغيير جميع كلمات المرور الافتراضية
3. فعّل النسخ الاحتياطي التلقائي
4. راقب سجلات الأخطاء بانتظام
5. حدث الشهادات الأمنية قبل انتهاء صلاحيتها

---

**تم إنشاء هذا الدليل بواسطة فريق تطوير خبرة المطابخ**
