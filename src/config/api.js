// API Configuration
// إعدادات API للبيئات المختلفة

const getApiConfig = () => {
  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    // Server-side rendering - return default config
    return {
      baseURL: '/api',
      uploadsURL: ''
    };
  }

  // تحديد البيئة الحالية
  const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  if (isDevelopment) {
    return {
      baseURL: 'http://localhost:3002',
      uploadsURL: '' // روابط نسبية - الصور في public/uploads
    };
  } else {
    // للإنتاج وجميع البيئات الأخرى
    return {
      baseURL: `${window.location.protocol}//${window.location.host}/api`,
      uploadsURL: '' // روابط نسبية - الصور في public/uploads
    };
  }
};

export const API_CONFIG = getApiConfig();

// دالة مساعدة لبناء URL كامل للصور
export const getImageURL = (imagePath) => {
  if (!imagePath) return null;

  // إذا كان الرابط كاملاً بالفعل، أرجعه كما هو
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // الصور الآن في public/uploads، لذا نستخدم روابط نسبية مباشرة
  // هذا يعمل مع HTTP و HTTPS تلقائياً
  return imagePath;
};

// دالة مساعدة لبناء URL للـ API
export const getApiURL = (endpoint) => {
  return `${API_CONFIG.baseURL}${endpoint}`;
};
