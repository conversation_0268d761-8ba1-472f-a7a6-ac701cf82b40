import { createContext, useContext, useState, useEffect } from 'react'
import { ADMIN_CONFIG, SECURITY_CONFIG } from '../../config/env'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState(null)

  // Secure admin credentials from environment
  const defaultCredentials = {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123'
  }

  useEffect(() => {
    // Clear any old stored credentials that might conflict
    localStorage.removeItem('admin_user_data')

    // Check if user is already logged in
    const token = localStorage.getItem('admin_token')
    const userData = localStorage.getItem('admin_user')
    const loginTime = localStorage.getItem('admin_login_time')

    if (token && userData && loginTime) {
      const currentTime = Date.now()
      const sessionAge = currentTime - parseInt(loginTime)

      // Check if session is still valid (not expired)
      if (sessionAge < SECURITY_CONFIG.sessionTimeout) {
        setIsAuthenticated(true)
        setUser(JSON.parse(userData))
      } else {
        // Session expired, clear data
        logout()
      }
    }

    setLoading(false)
  }, [])

  const login = (credentials) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Debug logging
        console.log('Login attempt:', credentials)
        console.log('Expected credentials:', defaultCredentials)

        // Use default credentials directly
        if (
          (credentials.username === defaultCredentials.username || credentials.email === defaultCredentials.email) &&
          credentials.password === defaultCredentials.password
        ) {
          const token = 'khobra_admin_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
          const loginTime = Date.now().toString()
          const userData = {
            username: defaultCredentials.username,
            email: defaultCredentials.email,
            loginTime: loginTime
          }

          localStorage.setItem('admin_token', token)
          localStorage.setItem('admin_user', JSON.stringify(userData))
          localStorage.setItem('admin_login_time', loginTime)

          setIsAuthenticated(true)
          setUser(userData)
          resolve(userData)
        } else {
          reject(new Error('بيانات الدخول غير صحيحة'))
        }
      }, 1000)
    })
  }

  const logout = () => {
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
    localStorage.removeItem('admin_login_time')
    setIsAuthenticated(false)
    setUser(null)
  }

  const updateUser = (userData) => {
    // Update stored user data
    const currentData = JSON.parse(localStorage.getItem('admin_user_data') || JSON.stringify(defaultCredentials))
    const updatedData = { ...currentData, ...userData }
    
    localStorage.setItem('admin_user_data', JSON.stringify(updatedData))
    
    // Update current user session if username/email changed
    if (userData.username || userData.email) {
      const updatedUser = {
        username: userData.username || user.username,
        email: userData.email || user.email
      }
      localStorage.setItem('admin_user', JSON.stringify(updatedUser))
      setUser(updatedUser)
    }
  }

  const value = {
    isAuthenticated,
    user,
    loading,
    login,
    logout,
    updateUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
