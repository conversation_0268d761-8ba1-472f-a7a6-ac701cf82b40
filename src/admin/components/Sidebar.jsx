import { useAuth } from '../context/AuthContext'

const Sidebar = ({ activeSection, setActiveSection, sidebarOpen, setSidebarOpen }) => {
  const { logout, user } = useAuth()

  const menuItems = [
    {
      id: 'dashboard',
      name: 'الرئيسية',
      icon: 'ri-dashboard-line',
      description: 'نظرة عامة على النظام'
    },
    {
      id: 'hero',
      name: 'إدارة الهيرو',
      icon: 'ri-image-line',
      description: 'تحرير صور ونصوص القسم الرئيسي'
    },
    {
      id: 'why-choose-us',
      name: 'لماذا تختارنا',
      icon: 'ri-star-line',
      description: 'إدارة مميزات الشركة'
    },
    {
      id: 'kitchens',
      name: 'إدارة المطابخ',
      icon: 'ri-home-4-line',
      description: 'إضافة وتعديل معرض المطابخ'
    },
    {
      id: 'cabinets',
      name: 'إدارة الخزائن',
      icon: 'ri-archive-line',
      description: 'إضافة وتعديل معرض الخزائن'
    },
    {
      id: 'footer',
      name: 'إدارة الفوتر',
      icon: 'ri-links-line',
      description: 'تحرير روابط التواصل والمعلومات'
    },
    {
      id: 'users',
      name: 'إدارة المستخدمين',
      icon: 'ri-user-settings-line',
      description: 'تعديل بيانات المستخدم'
    }
  ]

  const handleLogout = () => {
    if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      logout()
    }
  }

  return (
    <>
      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 right-0 z-50 w-80
        bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 backdrop-blur-2xl
        border-l border-white/20 shadow-2xl transform transition-all duration-700 ease-out
        lg:translate-x-0 lg:static lg:inset-0 relative overflow-hidden
        ${sidebarOpen ? 'translate-x-0' : 'translate-x-full'}
      `}>
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-20 -left-20 w-32 h-32 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 -right-10 w-24 h-24 bg-gradient-to-br from-indigo-400/10 to-blue-400/10 rounded-full blur-2xl animate-pulse delay-500"></div>
        </div>
        <div className="flex flex-col h-full relative z-10">
          {/* Header */}
          <div className="flex items-center justify-between p-8 border-b border-white/20 backdrop-blur-sm">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="relative group">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-3xl flex items-center justify-center shadow-2xl shadow-blue-500/30 transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                  <i className="ri-admin-line text-white text-3xl"></i>
                </div>
                <div className="absolute -inset-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl blur-lg opacity-30 animate-pulse group-hover:opacity-50 transition-opacity duration-300"></div>
                <div className="absolute -inset-1 bg-gradient-to-br from-blue-400 to-purple-500 rounded-3xl blur opacity-20 animate-pulse delay-500"></div>
              </div>
              <div className="space-y-1">
                <h2 className="text-2xl font-black bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">لوحة التحكم</h2>
                <p className="text-sm text-gray-600 font-bold tracking-wide">عجائب الخبراء</p>
                <div className="w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-3 rounded-2xl hover:bg-white/50 transition-all duration-300 hover:scale-110 hover:rotate-90 backdrop-blur-sm"
            >
              <i className="ri-close-line text-xl text-gray-600"></i>
            </button>
          </div>

          {/* User Info */}
          <div className="p-6 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-indigo-50/50 border-b border-white/20 backdrop-blur-sm relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5"></div>
            <div className="flex items-center space-x-4 rtl:space-x-reverse relative z-10">
              <div className="relative group">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl shadow-blue-500/25 transform transition-all duration-300 group-hover:scale-110">
                  <span className="text-white font-black text-xl">
                    {user?.username?.charAt(0)?.toUpperCase() || 'A'}
                  </span>
                </div>
                <div className="absolute -inset-1 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl blur opacity-30 animate-pulse"></div>
              </div>
              <div className="space-y-1">
                <h3 className="font-bold text-gray-900 text-lg">{user?.username || 'المدير'}</h3>
                <p className="text-sm text-gray-600 font-medium">{user?.email || '<EMAIL>'}</p>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-600 font-medium">متصل الآن</span>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-6 space-y-3 overflow-y-auto">
            {menuItems.map((item, index) => (
              <button
                key={item.id}
                onClick={() => {
                  setActiveSection(item.id)
                  setSidebarOpen(false)
                }}
                className={`
                  w-full text-right p-5 rounded-2xl transition-all duration-300 group relative overflow-hidden
                  transform hover:scale-[1.02] active:scale-[0.98]
                  ${activeSection === item.id
                    ? 'bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 text-white shadow-2xl shadow-blue-500/30'
                    : 'hover:bg-white/60 text-gray-700 hover:text-blue-600 backdrop-blur-sm hover:shadow-lg'
                  }
                `}
                style={{
                  animationDelay: `${index * 100}ms`
                }}
              >
                {/* Active indicator */}
                {activeSection === item.id && (
                  <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-2xl"></div>
                )}

                <div className="flex items-center space-x-4 rtl:space-x-reverse relative z-10">
                  <div className={`
                    w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-300
                    transform group-hover:scale-110 group-hover:rotate-3
                    ${activeSection === item.id
                      ? 'bg-white/20 shadow-lg'
                      : 'bg-gradient-to-br from-blue-50 to-purple-50 group-hover:from-blue-100 group-hover:to-purple-100 shadow-md'
                    }
                  `}>
                    <i className={`${item.icon} text-xl transition-all duration-300 ${
                      activeSection === item.id ? 'text-white' : 'text-gray-600 group-hover:text-blue-600'
                    }`}></i>
                  </div>
                  <div className="flex-1 text-right">
                    <h3 className="font-bold text-lg mb-1">{item.name}</h3>
                    <p className={`text-sm font-medium ${
                      activeSection === item.id ? 'text-white/90' : 'text-gray-500 group-hover:text-gray-600'
                    }`}>
                      {item.description}
                    </p>
                  </div>

                  {/* Arrow indicator */}
                  <div className={`
                    w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300
                    ${activeSection === item.id ? 'bg-white/20' : 'opacity-0 group-hover:opacity-100'}
                  `}>
                    <i className={`ri-arrow-left-s-line text-sm ${
                      activeSection === item.id ? 'text-white' : 'text-blue-600'
                    }`}></i>
                  </div>
                </div>
              </button>
            ))}
          </nav>

          {/* Logout Button */}
          <div className="p-6 border-t border-white/20 backdrop-blur-sm">
            <button
              onClick={handleLogout}
              className="w-full flex items-center justify-center space-x-3 rtl:space-x-reverse p-5
                         text-red-600 hover:text-white bg-red-50/50 hover:bg-gradient-to-r hover:from-red-500 hover:to-red-600
                         rounded-2xl transition-all duration-300 group transform hover:scale-[1.02] active:scale-[0.98]
                         border border-red-200/50 hover:border-red-500 shadow-lg hover:shadow-xl hover:shadow-red-500/25
                         backdrop-blur-sm relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-red-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <i className="ri-logout-box-line text-xl transition-all duration-300 group-hover:scale-110 relative z-10"></i>
              <span className="font-bold text-lg relative z-10">تسجيل الخروج</span>
            </button>
          </div>
        </div>
      </div>
    </>
  )
}

export default Sidebar
