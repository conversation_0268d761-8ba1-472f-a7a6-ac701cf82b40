import { useState } from 'react'
import { useAuth } from '../context/AuthContext'

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  const { login } = useAuth()

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      await login(formData)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Enhanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 via-purple-50 to-pink-50"></div>
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\'80\' height=\'80\' viewBox=\'0 0 80 80\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%239C92AC\' fill-opacity=\'0.04\'%3E%3Ccircle cx=\'40\' cy=\'40\' r=\'6\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

      {/* Enhanced Floating Elements */}
      <div className="absolute top-20 left-20 w-40 h-40 bg-gradient-to-br from-blue-400/15 via-purple-400/15 to-indigo-400/15 rounded-full blur-3xl animate-pulse float-animation"></div>
      <div className="absolute bottom-20 right-20 w-48 h-48 bg-gradient-to-br from-purple-400/15 via-pink-400/15 to-rose-400/15 rounded-full blur-3xl animate-pulse delay-1000 float-animation"></div>
      <div className="absolute top-1/2 left-10 w-32 h-32 bg-gradient-to-br from-indigo-400/15 via-blue-400/15 to-cyan-400/15 rounded-full blur-3xl animate-pulse delay-500 float-animation"></div>
      <div className="absolute top-10 right-1/3 w-24 h-24 bg-gradient-to-br from-cyan-400/10 via-blue-400/10 to-indigo-400/10 rounded-full blur-2xl animate-pulse delay-700 float-animation"></div>
      <div className="absolute bottom-1/3 left-1/4 w-36 h-36 bg-gradient-to-br from-rose-400/10 via-pink-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse delay-300 float-animation"></div>

      <div className="max-w-lg w-full mx-4 relative z-10">
        {/* Enhanced Logo and Header */}
        <div className="text-center mb-12 fade-in">
          <div className="relative inline-flex items-center justify-center w-32 h-32 mb-10 group">
            <div className="w-32 h-32 bg-gradient-to-br from-blue-500 via-purple-600 via-indigo-600 to-blue-700 rounded-full flex items-center justify-center shadow-2xl shadow-blue-500/30 transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-6 pulse-glow">
              <i className="ri-admin-line text-5xl text-white"></i>
            </div>
            <div className="absolute -inset-2 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-600 rounded-full blur-xl opacity-40 animate-pulse"></div>
            <div className="absolute -inset-4 bg-gradient-to-br from-blue-400 via-purple-500 to-indigo-500 rounded-full blur-2xl opacity-20 animate-pulse delay-500"></div>
          </div>
          <h1 className="text-5xl font-black bg-gradient-to-r from-gray-900 via-blue-800 via-purple-800 to-indigo-900 bg-clip-text text-transparent mb-4 tracking-tight">
            لوحة التحكم
          </h1>
          <p className="text-gray-700 text-xl font-bold mb-6">عجائب الخبراء - إدارة المحتوى</p>
          <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
            <div className="w-8 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            <div className="w-4 h-1 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full"></div>
            <div className="w-12 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
          </div>
        </div>

        {/* Enhanced Login Form */}
        <div className="glass-card p-12 scale-in backdrop-blur-2xl bg-white/80 border border-white/30 shadow-3xl relative overflow-hidden">
          {/* Form background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-blue-50/30 to-purple-50/30 rounded-3xl"></div>

          <form onSubmit={handleSubmit} className="space-y-8 relative z-10">
            <div className="space-y-3">
              <label htmlFor="username" className="block text-lg font-black text-gray-800 mb-4">
                اسم المستخدم أو البريد الإلكتروني
              </label>
              <div className="relative group">
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  className="form-input pl-16 h-16 text-lg font-medium"
                  placeholder=""
                  required
                />
                <div className="absolute left-5 top-1/2 transform -translate-y-1/2 transition-all duration-300 group-focus-within:text-blue-500 group-focus-within:scale-110">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center group-focus-within:from-blue-500 group-focus-within:to-purple-500 transition-all duration-300">
                    <i className="ri-user-line text-xl text-gray-500 group-focus-within:text-white"></i>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <label htmlFor="password" className="block text-lg font-black text-gray-800 mb-4">
                كلمة المرور
              </label>
              <div className="relative group">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="form-input pl-16 pr-16 h-16 text-lg font-medium"
                  placeholder=""
                  required
                />
                <div className="absolute left-5 top-1/2 transform -translate-y-1/2 transition-all duration-300 group-focus-within:text-blue-500 group-focus-within:scale-110">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center group-focus-within:from-blue-500 group-focus-within:to-purple-500 transition-all duration-300">
                    <i className="ri-lock-line text-xl text-gray-500 group-focus-within:text-white"></i>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-5 top-1/2 transform -translate-y-1/2 p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 hover:scale-110"
                >
                  <i className={`${showPassword ? 'ri-eye-off-line' : 'ri-eye-line'} text-xl text-gray-500 hover:text-blue-500`}></i>
                </button>
              </div>
            </div>

            {error && (
              <div className="bg-gradient-to-r from-red-50 to-rose-50 border border-red-200/50 rounded-2xl p-6 flex items-center space-x-4 rtl:space-x-reverse animate-pulse shadow-lg">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-rose-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <i className="ri-error-warning-line text-white text-xl"></i>
                </div>
                <span className="text-red-700 font-bold text-lg">{error}</span>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full h-16 btn-primary flex items-center justify-center space-x-4 rtl:space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed text-xl font-black shadow-2xl shadow-blue-500/30 relative overflow-hidden group"
            >
              {loading ? (
                <>
                  <div className="w-8 h-8 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>جاري تسجيل الدخول...</span>
                </>
              ) : (
                <>
                  <i className="ri-login-box-line text-2xl group-hover:scale-110 transition-transform duration-300"></i>
                  <span>تسجيل الدخول</span>
                </>
              )}
            </button>
          </form>


        </div>

        {/* Enhanced Footer */}
        <div className="text-center mt-12 fade-in">
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse mb-4">
            <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
            <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full animate-pulse delay-200"></div>
            <div className="w-2 h-2 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full animate-pulse delay-400"></div>
          </div>
          <p className="text-gray-600 font-bold text-lg">
            © 2024 عجائب الخبراء. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </div>
  )
}

export default Login
