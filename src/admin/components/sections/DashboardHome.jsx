import { useData } from '../../context/DataContext'
import { forceResetDatabase } from '../../utils/initDatabase'

const DashboardHome = () => {
  const { kitchensData, cabinetsData, whyChooseUsData, refreshData } = useData()

  const handleResetDatabase = async () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات الحالية.')) {
      try {
        await forceResetDatabase()
        await refreshData()
        alert('تم إعادة تعيين قاعدة البيانات بنجاح!')
        window.location.reload()
      } catch (error) {
        alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات')
        console.error(error)
      }
    }
  }

  const stats = [
    {
      title: 'إجمالي المطابخ',
      value: kitchensData?.length || 0,
      icon: 'ri-home-4-line',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-blue-100'
    },
    {
      title: 'إجمالي الخزائن',
      value: cabinetsData?.length || 0,
      icon: 'ri-archive-line',
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-50 to-purple-100'
    },
    {
      title: 'المميزات',
      value: whyChooseUsData?.features?.length || 0,
      icon: 'ri-star-line',
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-50 to-green-100'
    },
    {
      title: 'إجمالي الصور',
      value: (kitchensData?.reduce((acc, kitchen) => acc + (kitchen.images?.length || 0), 0) || 0) +
             (cabinetsData?.reduce((acc, cabinet) => acc + (cabinet.images?.length || 0), 0) || 0),
      icon: 'ri-image-line',
      color: 'from-orange-500 to-orange-600',
      bgColor: 'from-orange-50 to-orange-100'
    }
  ]

  const quickActions = [
    {
      title: 'إضافة مطبخ جديد',
      description: 'أضف تصميم مطبخ جديد إلى المعرض',
      icon: 'ri-add-line',
      color: 'from-blue-500 to-blue-600',
      action: 'kitchens'
    },
    {
      title: 'إضافة خزانة جديدة',
      description: 'أضف تصميم خزانة جديد إلى المعرض',
      icon: 'ri-add-line',
      color: 'from-purple-500 to-purple-600',
      action: 'cabinets'
    },
    {
      title: 'تحديث الهيرو',
      description: 'عدّل النصوص والصور في القسم الرئيسي',
      icon: 'ri-edit-line',
      color: 'from-green-500 to-green-600',
      action: 'hero'
    },
    {
      title: 'إدارة المميزات',
      description: 'أضف أو عدّل مميزات الشركة',
      icon: 'ri-settings-line',
      color: 'from-orange-500 to-orange-600',
      action: 'why-choose-us'
    }
  ]

  return (
    <div className="admin-dashboard space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">مرحباً بك في لوحة التحكم</h1>
            <p className="text-blue-100 text-lg">إدارة محتوى موقع عجائب الخبراء</p>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/20 rounded-2xl flex items-center justify-center">
              <i className="ri-dashboard-line text-4xl text-white"></i>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="stats-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
              </div>
              <div className={`w-14 h-14 bg-gradient-to-r ${stat.bgColor} rounded-xl flex items-center justify-center`}>
                <i className={`${stat.icon} text-2xl bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}></i>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="admin-card p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse">
          <i className="ri-flashlight-line text-blue-600"></i>
          <span>إجراءات سريعة</span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <button
              key={index}
              className="p-6 border border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-md transition-all duration-200 text-right group"
            >
              <div className={`w-12 h-12 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                <i className={`${action.icon} text-xl text-white`}></i>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">{action.title}</h3>
              <p className="text-sm text-gray-600">{action.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="admin-card p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse">
          <i className="ri-history-line text-blue-600"></i>
          <span>النشاط الأخير</span>
        </h2>
        <div className="space-y-4">
          <div className="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gray-50 rounded-lg">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <i className="ri-login-box-line text-blue-600"></i>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">تم تسجيل الدخول بنجاح</p>
              <p className="text-xs text-gray-500">{new Date().toLocaleString('ar-SA')}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gray-50 rounded-lg">
            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <i className="ri-check-line text-green-600"></i>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">تم تحميل البيانات بنجاح</p>
              <p className="text-xs text-gray-500">منذ دقائق قليلة</p>
            </div>
          </div>
        </div>
      </div>

      {/* System Info */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">معلومات النظام</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">إصدار النظام:</span>
              <span className="font-medium">v1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">آخر تحديث:</span>
              <span className="font-medium">اليوم</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">حالة النظام:</span>
              <span className="text-green-600 font-medium">متصل</span>
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={handleResetDatabase}
              className="w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm"
            >
              إعادة تعيين قاعدة البيانات
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">نصائح سريعة</h3>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start space-x-2 rtl:space-x-reverse">
              <i className="ri-lightbulb-line text-yellow-500 mt-0.5"></i>
              <span>استخدم صور عالية الجودة للحصول على أفضل النتائج</span>
            </div>
            <div className="flex items-start space-x-2 rtl:space-x-reverse">
              <i className="ri-lightbulb-line text-yellow-500 mt-0.5"></i>
              <span>احرص على كتابة أوصاف واضحة ومفيدة</span>
            </div>
            <div className="flex items-start space-x-2 rtl:space-x-reverse">
              <i className="ri-lightbulb-line text-yellow-500 mt-0.5"></i>
              <span>قم بحفظ التغييرات بانتظام</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardHome
