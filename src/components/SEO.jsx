import Head from 'next/head'
import { SEO_CONFIG, APP_CONFIG, COMPANY_CONFIG } from '../config/env'

const SEO = ({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  article = null
}) => {
  const siteTitle = title ? `${title} | ${SEO_CONFIG.title}` : SEO_CONFIG.title
  const siteDescription = description || SEO_CONFIG.description
  const siteKeywords = keywords || SEO_CONFIG.keywords
  const siteImage = image || SEO_CONFIG.ogImage
  const siteUrl = url || APP_CONFIG.url
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": COMPANY_CONFIG.name,
    "url": APP_CONFIG.url,
    "logo": `${APP_CONFIG.url}/images/logo.png`,
    "description": SEO_CONFIG.description,
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "SA",
      "addressLocality": "الرياض",
      "addressRegion": "الرياض"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": COMPANY_CONFIG.phone,
      "contactType": "customer service",
      "email": COMPANY_CONFIG.email
    },
    "sameAs": [
      "https://twitter.com/khobrakitchens",
      "https://instagram.com/khobrakitchens",
      "https://snapchat.com/add/khobrakitchens",
      "https://tiktok.com/@khobrakitchens"
    ]
  }

  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "الرئيسية",
        "item": APP_CONFIG.url
      }
    ]
  }

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{siteTitle}</title>
      <meta name="description" content={siteDescription} />
      <meta name="keywords" content={siteKeywords} />
      <meta name="author" content={SEO_CONFIG.author} />
      <meta name="robots" content="index, follow" />
      <meta name="language" content="ar" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={siteTitle} />
      <meta property="og:description" content={siteDescription} />
      <meta property="og:image" content={siteImage} />
      <meta property="og:url" content={siteUrl} />
      <meta property="og:site_name" content={COMPANY_CONFIG.name} />
      <meta property="og:locale" content="ar_SA" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={SEO_CONFIG.twitterCard} />
      <meta name="twitter:title" content={siteTitle} />
      <meta name="twitter:description" content={siteDescription} />
      <meta name="twitter:image" content={siteImage} />
      <meta name="twitter:site" content="@khobrakitchens" />
      
      {/* Article specific meta tags */}
      {article && (
        <>
          <meta property="article:author" content={article.author} />
          <meta property="article:published_time" content={article.publishedTime} />
          <meta property="article:modified_time" content={article.modifiedTime} />
          <meta property="article:section" content={article.section} />
          {article.tags && article.tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Canonical URL */}
      <link rel="canonical" href={siteUrl} />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbData)}
      </script>
      
      {/* Additional Meta Tags for Arabic Content */}
      <meta httpEquiv="Content-Language" content="ar" />
      <meta name="geo.region" content="SA" />
      <meta name="geo.placename" content="الرياض" />
      <meta name="geo.position" content="24.7136;46.6753" />
      <meta name="ICBM" content="24.7136, 46.6753" />
      
      {/* Mobile Optimization */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
      <meta name="format-detection" content="telephone=yes" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      
      {/* Performance and Security */}
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta name="referrer" content="origin-when-cross-origin" />
    </Head>
  )
}

export default SEO
