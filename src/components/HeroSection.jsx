import { useState, useEffect } from 'react'
import { getHeroData } from '../../database/api-client.js'

const HeroSection = () => {
  const [heroData, setHeroData] = useState(null);
  const [loading, setLoading] = useState(true);

  // جلب البيانات من الـ API
  useEffect(() => {
    const loadHeroData = async () => {
      try {
        setLoading(true)
        const data = await getHeroData()
        console.log('Loaded hero data:', data)
        setHeroData(data)
      } catch (error) {
        console.error('Error loading hero data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadHeroData()

    // Check for updates every 3 seconds
    const interval = setInterval(loadHeroData, 3000)

    return () => clearInterval(interval)
  }, [])

  const scrollToKitchens = () => {
    document.getElementById('kitchens')?.scrollIntoView({ behavior: 'smooth' });
  };

  if (!heroData) {
    return <div>جاري التحميل...</div>
  }

  return (
    <section
      id="home"
      className="min-h-screen flex items-center relative"
      style={{
        backgroundImage: `linear-gradient(to left, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7)), url('${heroData.background_image}')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="container mx-auto px-6 py-20 w-full relative z-10">
        <div className="max-w-4xl">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight animate-fade-in">
            {heroData.title}
          </h1>
          <p className="text-xl text-gray-100 mb-8 leading-relaxed animate-fade-in-delay">
            {heroData.subtitle}
          </p>
          <div className="flex flex-wrap gap-4 animate-fade-in-delay-2">
            <button
              onClick={scrollToKitchens}
              className="bg-primary hover:bg-blue-600 text-white px-8 py-4 rounded-button text-lg font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg whitespace-nowrap"
            >
              {heroData.primary_button_text}
            </button>
            <button
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-white px-8 py-4 rounded-button text-lg font-medium transition-all duration-300 transform hover:scale-105 whitespace-nowrap"
            >
              {heroData.secondary_button_text}
            </button>
          </div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <i className="ri-arrow-down-line text-2xl"></i>
      </div>
    </section>
  );
};

export default HeroSection;
