import { useState, useEffect } from 'react'
import { getFooterData } from '../../database/api-client.js'

const Footer = () => {
  const [footerData, setFooterData] = useState({
    socialMedia: [],
    quickLinks: [],
    contactInfo: [],
    copyright: ''
  });
  const [loading, setLoading] = useState(true);

  // جلب البيانات من الـ API
  useEffect(() => {
    const loadFooterData = async () => {
      try {
        setLoading(true)
        const data = await getFooterData()
        if (data && Object.keys(data).length > 0) {
          setFooterData(data)
        } else {
          setFooterData({
            socialMedia: [],
            quickLinks: [],
            contactInfo: [],
            copyright: ''
          })
        }
      } catch (error) {
        console.error('Error loading footer data:', error)
        setFooterData({
          socialMedia: [],
          quickLinks: [],
          contactInfo: [],
          copyright: ''
        })
      } finally {
        setLoading(false)
      }
    }

    loadFooterData()

    // إعادة تحميل البيانات كل 30 ثانية للحصول على التحديثات
    const interval = setInterval(loadFooterData, 30000)

    return () => clearInterval(interval)
  }, [])

  const services = [
    "تصميم وتنفيذ المطابخ",
    "تصميم وتنفيذ خزائن الملابس",
    "تصميم وحدات التلفزيون",
    "تصميم المكتبات والرفوف",
    "تصميم خزائن الحمامات"
  ];

  return (
    <footer id="contact" className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
          {/* Company Info */}
          <div>
            <div className="font-['Pacifico'] text-white text-3xl mb-6">logo</div>
            <p className="text-gray-400 mb-6">
              نحول مساحات منزلك إلى تحف فنية تجمع بين الجمال والوظيفة بلمسة من الإبداع
            </p>
            {footerData.socialMedia.length > 0 && (
              <div className="flex space-x-4 rtl:space-x-reverse">
                {footerData.socialMedia.map((social, index) => (
                  <a
                    key={index}
                    href={social.url}
                    className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center hover:bg-primary transition-colors duration-300"
                    title={social.platform}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <i className={social.icon}></i>
                  </a>
                ))}
              </div>
            )}
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-6">روابط سريعة</h3>
            <ul className="space-y-3">
              {footerData.quickLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    {link.text}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-xl font-bold mb-6">خدماتنا</h3>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index} className="text-gray-400">
                  {service}
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-bold mb-6">تواصل معنا</h3>
            <ul className="space-y-3">
              {footerData.contactInfo.map((info, index) => (
                <li key={index} className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-5 h-5 flex items-center justify-center mt-1">
                    <i className={`${info.icon} text-primary`}></i>
                  </div>
                  <span
                    className="text-gray-400"
                    style={{
                      direction: /\d/.test(info.text) ? 'ltr' : 'rtl',
                      textAlign: /\d/.test(info.text) ? 'left' : 'right'
                    }}
                  >
                    {info.text}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500">
          <p>{footerData.copyright}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
