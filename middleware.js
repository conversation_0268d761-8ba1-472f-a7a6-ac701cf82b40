import { NextResponse } from 'next/server'

export function middleware(request) {
  // Check if the request is for API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // Rewrite to external API server
    const apiUrl = new URL(request.nextUrl.pathname, 'http://localhost:3002')
    
    // Copy search params
    apiUrl.search = request.nextUrl.search
    
    return NextResponse.rewrite(apiUrl)
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: '/api/:path*'
}
