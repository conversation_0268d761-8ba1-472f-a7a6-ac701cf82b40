# Nginx Configuration for khobrakitchens.com
# This file should be placed in /etc/nginx/sites-available/ and symlinked to /etc/nginx/sites-enabled/

# Redirect www to non-www (301 Permanent Redirect)
server {
    listen 80;
    listen 443 ssl http2;
    server_name www.khobrakitchens.com;
    
    # SSL configuration (if using HTTPS)
    ssl_certificate /path/to/your/ssl/certificate.crt;
    ssl_certificate_key /path/to/your/ssl/private.key;
    
    # 301 redirect to non-www
    return 301 https://khobrakitchens.com$request_uri;
}

# Main server block (canonical domain)
server {
    listen 80;
    listen 443 ssl http2;
    server_name khobrakitchens.com;
    
    # SSL configuration
    ssl_certificate /path/to/your/ssl/certificate.crt;
    ssl_certificate_key /path/to/your/ssl/private.key;
    
    # Force HTTPS redirect
    if ($scheme != "https") {
        return 301 https://$host$request_uri;
    }
    
    # Document root
    root /var/www/html;
    index index.html;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Robots-Tag "index, follow" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    # Block access to admin panel from search engines
    location /admin {
        add_header X-Robots-Tag "noindex, nofollow, noarchive, nosnippet" always;
        try_files $uri $uri/ /index.html;
    }
    
    # Block access to sensitive files
    location ~ /\.(env|db)$ {
        deny all;
        return 404;
    }
    
    location /database/ {
        deny all;
        return 404;
    }
    
    # React Router (SPA) - serve index.html for all routes
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API routes (if you have backend API)
    location /api/ {
        # Proxy to your API server if needed
        # proxy_pass http://localhost:3001;
        # proxy_set_header Host $host;
        # proxy_set_header X-Real-IP $remote_addr;
    }
    
    # Robots.txt and sitemap
    location = /robots.txt {
        add_header Content-Type text/plain;
    }
    
    location = /sitemap.xml {
        add_header Content-Type application/xml;
    }
}
