# تكوين Nginx لموقع عجائب الخبراء

## نظرة عامة
تم تحديث تكوين Nginx لموقع khobrakitchens.com لتطبيق إعادة التوجيه الصحيحة من WWW إلى non-WWW وضمان استخدام HTTPS كنطاق أساسي.

## التغييرات المطبقة

### 1. إعادة التوجيه من WWW إلى non-WWW
```nginx
# إعادة توجيه www إلى non-www (HTTP)
server {
    listen 80;
    server_name www.khobrakitchens.com;
    return 301 http://khobrakitchens.com$request_uri;
}

# إعادة توجيه www إلى non-www (HTTPS)
server {
    listen 443 ssl;
    server_name www.khobrakitchens.com;
    ssl_certificate /etc/letsencrypt/live/khobrakitchens.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/khobrakitchens.com/privkey.pem;
    return 301 https://khobrakitchens.com$request_uri;
}
```

### 2. إعادة التوجيه من HTTP إلى HTTPS
```nginx
# إعادة التوجيه من HTTP إلى HTTPS
server {
    listen 80;
    server_name khobrakitchens.com;
    return 301 https://$host$request_uri;
}
```

### 3. السيرفر الرئيسي (HTTPS فقط)
```nginx
# السيرفر الرئيسي عبر HTTPS
server {
    listen 443 ssl;
    server_name khobrakitchens.com;
    
    root /var/www/html/dist;
    index index.html index.htm index.php;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/khobrakitchens.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/khobrakitchens.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    # React SPA Support
    location / {
        try_files $uri /index.html;
    }
    
    # API Proxy
    location /api/ {
        proxy_pass http://localhost:3002;
        # ... proxy headers
    }
}
```

## فوائد SEO

### 1. منع المحتوى المكرر
- ✅ جميع الطلبات تُوجه إلى `https://khobrakitchens.com`
- ✅ لا يوجد محتوى مكرر بين WWW و non-WWW
- ✅ تجنب تقسيم قوة الروابط بين النطاقين

### 2. تحسين تصنيفات محركات البحث
- ✅ نطاق أساسي واحد (canonical domain)
- ✅ إعادة توجيه 301 دائمة تحافظ على قوة SEO
- ✅ HTTPS إجباري لجميع الصفحات

### 3. تحسين تجربة المستخدم
- ✅ سرعة في التحميل (إعادة توجيه مباشرة)
- ✅ أمان عبر HTTPS
- ✅ عناوين URL متسقة

## الملفات المحدثة

### 1. ملف التكوين الرئيسي
- **المسار**: `/etc/nginx/sites-available/khobrakitchens.com`
- **النسخة الاحتياطية**: `khobrakitchens-fixed.conf` (في مجلد المشروع)

### 2. الأوامر المطبقة
```bash
# نسخ التكوين الجديد
sudo cp khobrakitchens-fixed.conf /etc/nginx/sites-available/khobrakitchens.com

# اختبار التكوين
sudo nginx -t

# إعادة تحميل Nginx
sudo systemctl reload nginx
```

## اختبار إعادة التوجيه

### اختبار WWW إلى non-WWW
```bash
curl -I http://www.khobrakitchens.com
# يجب أن يعيد: 301 Moved Permanently
# Location: http://khobrakitchens.com/

curl -I https://www.khobrakitchens.com
# يجب أن يعيد: 301 Moved Permanently
# Location: https://khobrakitchens.com/
```

### اختبار HTTP إلى HTTPS
```bash
curl -I http://khobrakitchens.com
# يجب أن يعيد: 301 Moved Permanently
# Location: https://khobrakitchens.com/
```

## الميزات الإضافية

### 1. الأمان
- حماية الملفات الحساسة (.env, .db)
- رؤوس الأمان (Security Headers)
- SSL/TLS محسن

### 2. الأداء
- ضغط Gzip للملفات
- تخزين مؤقت للملفات الثابتة
- دعم HTTP/2

### 3. دعم التطبيق
- دعم React SPA (Single Page Application)
- Proxy للـ API على المنفذ 3002
- دعم PHP (اختياري)

## ملاحظات مهمة

1. **النطاق الأساسي**: `https://khobrakitchens.com` (بدون www)
2. **إعادة التوجيه**: جميع الطلبات تُوجه إلى النطاق الأساسي
3. **SSL**: إجباري لجميع الصفحات
4. **API**: متاح على `/api/` ويُوجه إلى المنفذ 3002

## التحقق من الحالة
```bash
# حالة Nginx
sudo systemctl status nginx

# اختبار التكوين
sudo nginx -t

# إعادة تحميل التكوين
sudo systemctl reload nginx
```

---

**تاريخ التحديث**: 2025-07-05  
**المطور**: Augment Agent  
**الحالة**: مطبق ومفعل
