# اختبار تزامن بيانات Hero Section
# Hero Section Data Sync Testing

## المشاكل التي تم حلها:

### 1. **مشكلة عدم انعكاس التعديلات على الصفحة الرئيسية**
**الحل:**
- تم إصلاح API client لجلب ID الصحيح قبل التحديث
- تم إضافة إعادة تحميل البيانات بعد التحديث في DataContext
- تم إضافة نظام إشعارات باستخدام localStorage
- تم إضافة فحص دوري للتحديثات كل ثانيتين

### 2. **مشكلة اختفاء التعديلات عند تحديث صفحة الإدارة**
**الحل:**
- تم التأكد من حفظ البيانات في قاعدة البيانات SQLite
- تم إصلاح تحميل البيانات من API بدلاً من localStorage
- تم إضافة endpoint للتحقق من آخر وقت تحديث

## كيفية الاختبار:

### الخطوة 1: تأكد من تشغيل الخدمات
```bash
pm2 list
# يجب أن ترى khobra-api و khobra-frontend يعملان
```

### الخطوة 2: افتح الصفحة الرئيسية
```
http://localhost:4173
```

### الخطوة 3: افتح صفحة الإدارة في تبويب آخر
```
http://localhost:4173/admin
```

### الخطوة 4: قم بتسجيل الدخول
- اسم المستخدم: `admin_khobra_kitchens`
- كلمة المرور: `khobra_admin_2024`

### الخطوة 5: اذهب إلى إدارة Hero Section
- اضغط على "إدارة قسم الهيرو"
- اضغط على "تعديل"

### الخطوة 6: قم بتعديل البيانات
- غير العنوان أو الوصف
- اضغط على "حفظ التغييرات"

### الخطوة 7: تحقق من التحديث
- يجب أن ترى رسالة "تم حفظ التغييرات بنجاح!"
- ارجع للصفحة الرئيسية
- يجب أن ترى التغييرات خلال 2-3 ثواني

### الخطوة 8: اختبار الثبات
- حدث صفحة الإدارة (F5)
- يجب أن تبقى التعديلات موجودة
- حدث الصفحة الرئيسية (F5)
- يجب أن تبقى التعديلات موجودة

## التحقق من API مباشرة:

### عرض البيانات الحالية:
```bash
curl http://localhost:3002/api/hero
```

### عرض آخر وقت تحديث:
```bash
curl http://localhost:3002/api/hero/last-update
```

### تحديث البيانات مباشرة:
```bash
curl -X PUT http://localhost:3002/api/hero/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "عنوان جديد",
    "subtitle": "وصف جديد",
    "background_image": "https://example.com/image.jpg",
    "primary_button_text": "زر أول",
    "secondary_button_text": "زر ثاني"
  }'
```

## آلية التزامن:

### 1. **التحديث الفوري:**
- عند الحفظ في الإدارة، يتم إرسال إشعار localStorage
- الصفحة الرئيسية تستمع لهذا الإشعار وتحدث البيانات فوراً

### 2. **التحديث الدوري:**
- الصفحة الرئيسية تتحقق من آخر وقت تحديث كل ثانيتين
- إذا تغير الوقت، تعيد تحميل البيانات

### 3. **الحفظ الدائم:**
- جميع البيانات محفوظة في قاعدة البيانات SQLite
- لا تعتمد على localStorage أو ذاكرة المتصفح

## استكشاف الأخطاء:

### إذا لم تظهر التحديثات:
1. تحقق من console المتصفح للأخطاء
2. تأكد من أن API يعمل: `curl http://localhost:3002/api/hero`
3. تحقق من سجلات PM2: `pm2 logs khobra-api`

### إذا اختفت التعديلات:
1. تحقق من قاعدة البيانات: `curl http://localhost:3002/api/hero`
2. تأكد من أن التحديث تم بنجاح في API
3. تحقق من سجلات الأخطاء: `pm2 logs`

## الملفات المعدلة:

1. `database/api-client.js` - إصلاح updateHeroData
2. `src/admin/context/DataContext.jsx` - إضافة إعادة التحميل
3. `src/components/HeroSection.jsx` - تحسين نظام التحديث
4. `src/admin/components/sections/HeroManagement.jsx` - إضافة إشعارات
5. `api/server.js` - إضافة endpoint للتحقق من التحديثات

---

**ملاحظة:** النظام الآن يدعم التزامن الفوري والدائم بين صفحة الإدارة والصفحة الرئيسية.
