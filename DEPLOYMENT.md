# دليل النشر لموقع خبرة المطابخ
# Khobra Kitchens Deployment Guide

## 🚀 نشر الموقع في الإنتاج

### 1. إعداد الخادم

#### أ) رفع الملفات:
```bash
# رفع جميع ملفات المشروع إلى الخادم
scp -r /var/www/html/* <EMAIL>:/var/www/html/
```

#### ب) تثبيت التبعيات:
```bash
# في مجلد الـ API
cd /var/www/html/api
npm install

# في مجلد الواجهة الأمامية
cd /var/www/html
npm install
```

### 2. إعداد قاعدة البيانات

```bash
# إنشاء مجلد قاعدة البيانات
mkdir -p /var/www/html/database

# نسخ ملف قاعدة البيانات
cp khobra_kitchens.db /var/www/html/database/
```

### 3. إ<PERSON><PERSON><PERSON> الخادم (Apache/Nginx)

#### Apache (.htaccess):
```apache
RewriteEngine On
RewriteRule ^api/(.*)$ /api/server.js/$1 [L,P]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

#### Nginx:
```nginx
server {
    listen 80;
    server_name khobrakitchens.com;
    root /var/www/html/dist;
    index index.html;

    # API Proxy
    location /api/ {
        proxy_pass http://localhost:3002/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Static files (uploads) - الآن في public/uploads
    location /uploads/ {
        alias /var/www/html/dist/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Frontend routing
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 4. بناء الواجهة الأمامية

```bash
cd /var/www/html
npm run build
```

### 5. تشغيل الخادم

#### باستخدام PM2 (موصى به):
```bash
# تثبيت PM2
npm install -g pm2

# تشغيل الخادم
cd /var/www/html/api
pm2 start server.js --name "khobra-api"

# حفظ إعدادات PM2
pm2 save
pm2 startup
```

#### تشغيل عادي:
```bash
cd /var/www/html/api
node server.js &
```

### 6. إعداد HTTPS

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx

# الحصول على شهادة SSL
sudo certbot --nginx -d khobrakitchens.com
```

### 7. التحقق من التشغيل

1. **API**: `https://khobrakitchens.com/api/kitchens`
2. **الموقع**: `https://khobrakitchens.com`
3. **الصور**: `https://khobrakitchens.com/uploads/kitchens/...`

## 🔧 إعدادات البيئة

الموقع يتعرف تلقائياً على البيئة:

- **التطوير**: `localhost` → `http://localhost:3002`
- **الإنتاج**: `khobrakitchens.com` → `https://khobrakitchens.com/api`

## 📁 هيكل الملفات في الإنتاج

```
/var/www/html/
├── dist/                 # الواجهة الأمامية المبنية
├── api/                  # خادم API
│   ├── server.js
│   ├── uploads/          # الصور المرفوعة
│   └── node_modules/
├── database/             # قاعدة البيانات
│   └── khobra_kitchens.db
└── src/                  # الكود المصدري
```

## 🛠️ استكشاف الأخطاء

### مشكلة Mixed Content:
- تأكد من أن جميع الروابط تستخدم HTTPS
- تحقق من إعدادات الخادم

### مشكلة الصور:
- تأكد من وجود مجلد `/uploads`
- تحقق من صلاحيات الملفات
- تأكد من إعداد Nginx/Apache للملفات الثابتة

### مشكلة API:
- تحقق من تشغيل خادم Node.js
- تأكد من إعدادات Proxy
- راجع logs الخادم

## 📝 ملاحظات مهمة

1. **قاعدة البيانات**: تأكد من نسخ احتياطي منتظم
2. **الصور**: تأكد من backup مجلد uploads
3. **SSL**: تجديد الشهادة كل 90 يوم
4. **PM2**: مراقبة حالة الخادم بانتظام

## 🔄 تحديث الموقع

```bash
# إيقاف الخادم
pm2 stop khobra-api

# رفع الملفات الجديدة
# بناء الواجهة الأمامية
npm run build

# إعادة تشغيل الخادم
pm2 restart khobra-api
```
