version: '3.8'

services:
  # Main web application
  khobra-kitchens:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: khobra-kitchens-web
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # SSL certificates (update paths as needed)
      - /etc/ssl/certs:/etc/ssl/certs:ro
      - /etc/ssl/private:/etc/ssl/private:ro
      # Nginx logs
      - ./logs/nginx:/var/log/nginx
      # Custom nginx config (if needed)
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
    environment:
      - NGINX_HOST=khobrakitchens.com
      - NGINX_PORT=80
      - TZ=Asia/Riyadh
    networks:
      - khobra-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.khobra.rule=Host(`khobrakitchens.com`,`www.khobrakitchens.com`)"
      - "traefik.http.routers.khobra.entrypoints=websecure"
      - "traefik.http.routers.khobra.tls.certresolver=letsencrypt"
      - "traefik.http.services.khobra.loadbalancer.server.port=80"

  # Reverse proxy (optional - using Traefik)
  traefik:
    image: traefik:v2.10
    container_name: khobra-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik:/etc/traefik
      - ./ssl:/ssl
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/ssl/acme.json
      - --log.level=INFO
    networks:
      - khobra-network
    profiles:
      - traefik

  # Database (if needed for future features)
  database:
    image: mysql:8.0
    container_name: khobra-database
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: khobra_root_password_2024
      MYSQL_DATABASE: khobra_kitchens
      MYSQL_USER: khobra_user
      MYSQL_PASSWORD: khobra_password_2024
    volumes:
      - db_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - khobra-network
    profiles:
      - database

  # Redis for caching (if needed)
  redis:
    image: redis:7-alpine
    container_name: khobra-redis
    restart: unless-stopped
    command: redis-server --requirepass khobra_redis_password_2024
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - khobra-network
    profiles:
      - cache

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: khobra-prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - khobra-network
    profiles:
      - monitoring

  # Grafana for dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: khobra-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=khobra_grafana_admin_2024
    volumes:
      - grafana_data:/var/lib/grafana
    ports:
      - "3000:3000"
    networks:
      - khobra-network
    profiles:
      - monitoring

networks:
  khobra-network:
    driver: bridge

volumes:
  db_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
