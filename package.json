{"name": "khobra-kitchens-nextjs", "private": true, "version": "1.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "export": "next build && next export", "lint": "next lint", "api": "cd api && npm install && npm start", "api:dev": "cd api && npm install && npm run dev", "pm2:start": "pm2 start ecosystem.config.cjs --env production", "pm2:stop": "pm2 stop all", "pm2:restart": "pm2 restart all", "pm2:status": "pm2 list", "pm2:logs": "pm2 logs", "pm2:save": "pm2 save", "deploy": "./pm2-management.sh restart && ./pm2-management.sh save"}, "dependencies": {"framer-motion": "^12.20.1", "next": "^15.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "sql.js": "^1.13.0", "swiper": "^11.2.10"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "^15.4.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.3"}}