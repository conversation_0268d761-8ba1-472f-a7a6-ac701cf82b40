# إعداد PM2 لخبرة المطابخ
# PM2 Setup for Khobra Kitchens

## نظرة عامة | Overview

تم إعداد PM2 لإدارة خدمات خبرة المطابخ تلقائياً على السيرفر. النظام يدير خدمتين رئيسيتين:

PM2 has been configured to automatically manage Khobra Kitchens services on the server. The system manages two main services:

1. **khobra-api** - خادم API (المنفذ 3002)
2. **khobra-frontend** - الواجهة الأمامية (المنفذ 4173)

## الملفات المهمة | Important Files

- `ecosystem.config.cjs` - ملف إعداد PM2
- `pm2-management.sh` - سكريبت إدارة الخدمات
- `/root/.pm2/logs/` - مجلد السجلات

## الأوامر الأساسية | Basic Commands

### عرض حالة الخدمات | Check Services Status
```bash
pm2 list
# أو
./pm2-management.sh status
```

### تشغيل الخدمات | Start Services
```bash
pm2 start ecosystem.config.cjs --env production
# أو
./pm2-management.sh start
```

### إيقاف الخدمات | Stop Services
```bash
pm2 stop all
# أو
./pm2-management.sh stop
```

### إعادة تشغيل الخدمات | Restart Services
```bash
pm2 restart all
# أو
./pm2-management.sh restart
```

### عرض السجلات | View Logs
```bash
# جميع السجلات
pm2 logs
./pm2-management.sh logs

# سجلات API فقط
pm2 logs khobra-api
./pm2-management.sh logs-api

# سجلات الواجهة فقط
pm2 logs khobra-frontend
./pm2-management.sh logs-web
```

### مراقبة الأداء | Monitor Performance
```bash
pm2 monit
# أو
./pm2-management.sh monit
```

## إعدادات التشغيل التلقائي | Auto-Start Configuration

تم إعداد PM2 للتشغيل التلقائي عند إعادة تشغيل السيرفر:

```bash
# تم تنفيذ هذه الأوامر بالفعل
pm2 startup
pm2 save
```

## مسارات السجلات | Log Paths

- **API Logs**: `/root/.pm2/logs/khobra-api-out.log`
- **API Errors**: `/root/.pm2/logs/khobra-api-error.log`
- **Frontend Logs**: `/root/.pm2/logs/khobra-frontend-out.log`
- **Frontend Errors**: `/root/.pm2/logs/khobra-frontend-error.log`

## اختبار الخدمات | Testing Services

### اختبار API
```bash
curl http://localhost:3002/api/categories
```

### اختبار الواجهة الأمامية
```bash
curl http://localhost:4173
```

## استكشاف الأخطاء | Troubleshooting

### إذا لم تعمل الخدمات | If Services Don't Work
```bash
# تحقق من الحالة
pm2 list

# تحقق من السجلات
pm2 logs

# إعادة تشغيل
pm2 restart all
```

### إذا فشل API في الاتصال بقاعدة البيانات | If API Fails to Connect to Database
```bash
# تحقق من وجود قاعدة البيانات
ls -la database/khobra_kitchens.db

# تحقق من سجلات API
pm2 logs khobra-api
```

## أوامر الصيانة | Maintenance Commands

### حفظ الحالة الحالية | Save Current State
```bash
pm2 save
```

### إعادة تحميل الإعدادات | Reload Configuration
```bash
pm2 reload ecosystem.config.cjs --env production
```

### حذف جميع العمليات | Delete All Processes
```bash
pm2 delete all
```

### إعادة تشغيل من الملف | Restart from Config File
```bash
pm2 start ecosystem.config.cjs --env production
```

## معلومات الشبكة | Network Information

- **API Server**: http://localhost:3002
- **Frontend Server**: http://localhost:4173
- **Public Website**: https://khobrakitchens.com
- **Admin Panel**: https://khobrakitchens.com/admin

## الأمان | Security

- جميع الخدمات تعمل تحت المستخدم `root`
- السجلات محفوظة في `/root/.pm2/logs/`
- قاعدة البيانات SQLite محمية بصلاحيات النظام

## الدعم | Support

للحصول على المساعدة:
1. تحقق من السجلات أولاً
2. استخدم `pm2 monit` لمراقبة الأداء
3. راجع هذا الملف للأوامر الأساسية

---

**ملاحظة**: تأكد من أن جميع التبعيات مثبتة قبل تشغيل الخدمات.
**Note**: Ensure all dependencies are installed before running services.
