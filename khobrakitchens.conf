# Redirect www to non-www (HTTP)
server {
    listen 80;
    server_name www.khobrakitchens.com;

    # 301 Permanent Redirect to non-www
    return 301 http://khobrakitchens.com$request_uri;
}

# Main server block (canonical domain - non-www)
server {
    listen 80;
    server_name khobrakitchens.com;

    root /var/www/html/dist;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Serve uploaded files directly from public/uploads
    location /uploads/ {
        alias /var/www/html/public/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Handle React Router (SPA)
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|json|lock)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Robots.txt and sitemap
    location = /robots.txt {
        access_log off;
        log_not_found off;
    }

    location = /sitemap.xml {
        access_log off;
        log_not_found off;
    }

    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;

    # Logging
    access_log /var/log/nginx/khobrakitchens.com.access.log;
    error_log /var/log/nginx/khobrakitchens.com.error.log;
}
