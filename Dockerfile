# K<PERSON><PERSON> Kitchens - Docker Configuration
# Multi-stage build for production

# Stage 1: Build the application
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production --legacy-peer-deps

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Production server
FROM nginx:alpine AS production

# Install additional packages
RUN apk add --no-cache \
    curl \
    tzdata

# Set timezone to Saudi Arabia
RUN cp /usr/share/zoneinfo/Asia/Riyadh /etc/localtime && \
    echo "Asia/Riyadh" > /etc/timezone

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy additional nginx files
COPY public/.htaccess /usr/share/nginx/html/
COPY public/robots.txt /usr/share/nginx/html/
COPY public/sitemap.xml /usr/share/nginx/html/
COPY public/site.webmanifest /usr/share/nginx/html/

# Create nginx user and set permissions
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -u 1001 -G nginx && \
    chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Expose port
EXPOSE 80 443

# Labels for metadata
LABEL maintainer="Khobra Kitchens <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="Khobra Kitchens - Kitchen and Cabinet Design Website"
LABEL domain="khobrakitchens.com"

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
