#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// إنشاء مجلد data في dist إذا لم يكن موجوداً
const distDataDir = path.join(__dirname, '..', 'dist', 'data');
if (!fs.existsSync(distDataDir)) {
  fs.mkdirSync(distDataDir, { recursive: true });
}

// نسخ ملف kitchens.json من public إلى dist
const sourceFile = path.join(__dirname, '..', 'public', 'data', 'kitchens.json');
const destFile = path.join(distDataDir, 'kitchens.json');

if (fs.existsSync(sourceFile)) {
  fs.copyFileSync(sourceFile, destFile);
  console.log('✓ تم نسخ ملف kitchens.json إلى مجلد dist');
} else {
  console.log('⚠ ملف kitchens.json غير موجود في مجلد public/data');
}
