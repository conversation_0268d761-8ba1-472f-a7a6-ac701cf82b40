#!/bin/bash

# Script to sync uploaded files from public/uploads to dist/uploads
# This ensures that newly uploaded images are immediately available via nginx

SOURCE_DIR="/var/www/html/public/uploads"
DEST_DIR="/var/www/html/dist/uploads"

# Create destination directory if it doesn't exist
mkdir -p "$DEST_DIR"

# Sync files from public/uploads to dist/uploads
rsync -av --delete "$SOURCE_DIR/" "$DEST_DIR/"

echo "Upload sync completed: $(date)"
