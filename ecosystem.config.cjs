// PM2 Ecosystem Configuration for Khobra Kitchens
// ملف إعداد PM2 لخبرة المطابخ

module.exports = {
  apps: [
    {
      name: 'khobra-api',
      script: 'server.js',
      cwd: './api',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      log_file: '/root/.pm2/logs/khobra-api.log',
      out_file: '/root/.pm2/logs/khobra-api-out.log',
      error_file: '/root/.pm2/logs/khobra-api-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      ignore_watch: ['node_modules', 'logs'],
      autorestart: true
    },
    {
      name: 'khobra-frontend',
      script: 'npx',
      args: 'vite preview --host 0.0.0.0 --port 4173',
      cwd: '/var/www/html',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 4173
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 4173
      },
      log_file: '/root/.pm2/logs/khobra-frontend.log',
      out_file: '/root/.pm2/logs/khobra-frontend-out.log',
      error_file: '/root/.pm2/logs/khobra-frontend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'dist'],
      autorestart: true
    }
  ],

  deploy: {
    production: {
      user: 'root',
      host: '************',
      ref: 'origin/main',
      repo: '**************:your-repo/khobra-kitchens.git',
      path: '/var/www/html',
      'pre-deploy-local': '',
      'post-deploy': 'npm install --legacy-peer-deps && npm run build && pm2 reload ecosystem.config.cjs --env production',
      'pre-setup': ''
    }
  }
};
