import { useRef, useState, useEffect } from 'react';
import Head from 'next/head';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { getCabinetsData, getFooterData } from '../database/api-client.js';
import { getImageURL } from '../src/config/api.js';
import Navbar from '../src/components/Navbar';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const CabinetsPage = () => {
  const sectionRef = useRef(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  const [cabinets, setCabinets] = useState([]);
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [socialMedia, setSocialMedia] = useState([]);

  // قراءة البيانات من قاعدة البيانات عند تحميل المكون
  useEffect(() => {
    const loadCabinetsData = async () => {
      try {
        const cabinetsData = await getCabinetsData();
        console.log('Loaded cabinets data:', cabinetsData);
        if (cabinetsData && cabinetsData.length > 0) {
          // تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
          const formattedCabinets = cabinetsData.map(cabinet => ({
            id: cabinet.id,
            title: cabinet.title,
            description: cabinet.description,
            category: cabinet.category_slug || 'wardrobe-cabinets',
            images: cabinet.images?.map(img => {
              const imageUrl = typeof img === 'string' ? img : (img.image_url || img);
              return getImageURL(imageUrl);
            }) || []
          }));
          console.log('Formatted cabinets:', formattedCabinets);
          setCabinets(formattedCabinets);
        } else {
          console.log('No cabinets data found, using fallback data');
          // استخدام بيانات احتياطية إذا لم تكن هناك بيانات في قاعدة البيانات
          setCabinets(fallbackCabinets);
        }
      } catch (error) {
        console.error('Error loading cabinets data:', error);
        // استخدام بيانات احتياطية في حالة الخطأ
        setCabinets(fallbackCabinets);
      }
    };

    const loadFooterData = async () => {
      try {
        const footerData = await getFooterData();
        if (footerData) {
          setWhatsappNumber(footerData.whatsapp_number || '966501234567');
          setSocialMedia(footerData.social_media || []);
        }
      } catch (error) {
        console.error('Error loading footer data:', error);
        setWhatsappNumber('966501234567');
        setSocialMedia([]);
      }
    };

    loadCabinetsData();
    loadFooterData();
  }, []);

  // بيانات احتياطية للخزائن
  const fallbackCabinets = [
    {
      id: 1,
      title: 'خزانة ملابس عصرية',
      description: 'تصميم خزانة ملابس عصرية بأحدث التقنيات والمواد عالية الجودة',
      category: 'wardrobe-cabinets',
      images: [
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'
      ]
    },
    // يمكن إضافة المزيد من البيانات الاحتياطية هنا
  ];

  return (
    <>
      <Head>
        <title>معرض الخزائن العصرية والكلاسيكية - عجائب الخبراء</title>
        <meta name="description" content="استكشف مجموعتنا المتنوعة من تصاميم الخزائن العصرية والكلاسيكية وخزائن الملابس والمكاتب. أحدث تصاميم الخزائن في السعودية مع عجائب الخبراء" />
        <meta name="keywords" content="خزائن ملابس, خزائن عصرية, خزائن كلاسيكية, خزائن مكاتب, تصاميم خزائن, معرض خزائن, عجائب الخبراء" />
        <link rel="canonical" href="https://khobrakitchens.com/cabinets" />
        <meta property="og:title" content="معرض الخزائن العصرية والكلاسيكية - عجائب الخبراء" />
        <meta property="og:description" content="استكشف مجموعتنا المتنوعة من تصاميم الخزائن العصرية والكلاسيكية وخزائن الملابس والمكاتب." />
        <meta property="og:url" content="https://khobrakitchens.com/cabinets" />
        <meta property="og:type" content="website" />
      </Head>

      <div className="bg-gray-50">
        <Navbar />
        {/* سيتم إضافة باقي المحتوى هنا */}
        <div className="min-h-screen pt-20">
          <div className="container mx-auto px-4 py-8">
            <h1 className="text-4xl font-bold text-center mb-8">معرض الخزائن</h1>
            <p className="text-center text-gray-600 mb-12">
              استكشف مجموعتنا المتنوعة من تصاميم الخزائن العصرية والكلاسيكية
            </p>
            
            {/* سيتم إضافة معرض الخزائن هنا */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {cabinets.map((cabinet) => (
                <div key={cabinet.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
                  {cabinet.images && cabinet.images.length > 0 && (
                    <img 
                      src={cabinet.images[0]} 
                      alt={cabinet.title}
                      className="w-full h-64 object-cover"
                    />
                  )}
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-2">{cabinet.title}</h3>
                    <p className="text-gray-600">{cabinet.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  )
};

export default CabinetsPage;
