// Hero API Route for Next.js
export default async function handler(req, res) {
  const apiUrl = 'http://localhost:3002/api/hero';
  
  try {
    const response = await fetch(apiUrl, {
      method: req.method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined,
    });
    
    const data = await response.json();
    res.status(response.status).json(data);
  } catch (error) {
    console.error('Hero API Error:', error);
    res.status(500).json({ 
      error: 'Internal Server Error',
      message: 'Failed to connect to API server'
    });
  }
}
