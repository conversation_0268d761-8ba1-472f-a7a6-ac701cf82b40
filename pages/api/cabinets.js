export default async function handler(req, res) {
  try {
    const response = await fetch('http://localhost:3002/api/cabinets', {
      method: req.method,
      headers: { 'Content-Type': 'application/json' },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined,
    });
    const data = await response.json();
    res.status(response.status).json(data);
  } catch (error) {
    res.status(500).json({ error: 'API Error', message: error.message });
  }
}
