// API Proxy for Next.js
// This file proxies all API requests to the external Express server

export default async function handler(req, res) {
  const { slug } = req.query;
  const path = Array.isArray(slug) ? slug.join('/') : slug;

  // Build the external API URL
  const apiUrl = `http://localhost:3002/api/${path}`;

  console.log('API Proxy Request:', {
    method: req.method,
    path,
    apiUrl,
    slug
  });

  try {
    // Forward the request to the external API
    const response = await fetch(apiUrl, {
      method: req.method,
      headers: {
        'Content-Type': 'application/json',
        // Don't forward all headers to avoid conflicts
        'User-Agent': 'Next.js API Proxy',
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined,
    });

    console.log('API Response Status:', response.status);

    // Get the response data
    const data = await response.json();

    console.log('API Response Data:', data);

    // Forward the response
    res.status(response.status).json(data);
  } catch (error) {
    console.error('API Proxy Error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to connect to API server',
      details: error.message
    });
  }
}
