import '../src/index.css'
import { useEffect, useState } from 'react'
import { initializeDatabase } from '../src/admin/utils/initDatabase'

export default function App({ Component, pageProps }) {
  useEffect(() => {
    // Initialize database on app start (client-side only)
    if (typeof window !== 'undefined') {
      initializeDatabase().catch(console.error)
    }
  }, [])

  return (
    <div dir="rtl">
      <Component {...pageProps} />
    </div>
  )
}
