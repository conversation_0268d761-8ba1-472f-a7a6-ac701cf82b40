import '../src/index.css'
import { useEffect, useState } from 'react'
import { initializeDatabase } from '../src/admin/utils/initDatabase'

export default function App({ Component, pageProps }) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    // Set client-side flag
    setIsClient(true)

    // Initialize database on app start (client-side only)
    if (typeof window !== 'undefined') {
      initializeDatabase().catch(console.error)
    }
  }, [])

  // Don't render anything until we're on the client side
  if (!isClient) {
    return null
  }

  return (
    <div dir="rtl">
      <Component {...pageProps} />
    </div>
  )
}
