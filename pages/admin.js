import { useEffect, useState } from 'react'
import Head from 'next/head'
import Login from '../src/admin/components/Login'
import Dashboard from '../src/admin/components/Dashboard'
import { AuthProvider, useAuth } from '../src/admin/context/AuthContext'
import { DataProvider } from '../src/admin/context/DataContext'
import { initializeDatabase } from '../src/admin/utils/initDatabase'

function AdminContent() {
  const { isAuthenticated, loading } = useAuth()
  const [dbInitialized, setDbInitialized] = useState(false)
  const [dbLoading, setDbLoading] = useState(true)

  useEffect(() => {
    const initDB = async () => {
      try {
        setDbLoading(true)
        await initializeDatabase()
        setDbInitialized(true)
      } catch (error) {
        console.error('Failed to initialize database:', error)
        setDbInitialized(false)
      } finally {
        setDbLoading(false)
      }
    }

    initDB()
  }, [])

  if (loading || dbLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
          <p className="text-gray-600 text-xl font-bold">
            {dbLoading ? 'جاري تهيئة قاعدة البيانات...' : 'جاري التحميل...'}
          </p>
        </div>
      </div>
    )
  }

  if (!dbInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i className="ri-error-warning-line text-2xl text-red-500"></i>
          </div>
          <h2 className="text-2xl font-bold text-red-600 mb-4">خطأ في تهيئة قاعدة البيانات</h2>
          <p className="text-gray-600 mb-6">
            حدث خطأ أثناء تهيئة قاعدة البيانات. يرجى إعادة تحميل الصفحة أو الاتصال بالدعم التقني.
          </p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-bold transition-colors"
          >
            إعادة تحميل الصفحة
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>لوحة الإدارة - عجائب الخبراء</title>
        <meta name="description" content="لوحة إدارة موقع عجائب الخبراء - منطقة محظورة للمديرين فقط" />
        <meta name="robots" content="noindex, nofollow, noarchive, nosnippet" />
        <meta name="googlebot" content="noindex, nofollow" />
        <meta name="bingbot" content="noindex, nofollow" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        {isAuthenticated ? (
          <DataProvider>
            <Dashboard />
          </DataProvider>
        ) : (
          <Login />
        )}
      </div>
    </>
  )
}

function AdminPage() {
  return (
    <AuthProvider>
      <AdminContent />
    </AuthProvider>
  )
}

export default AdminPage
