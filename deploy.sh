#!/bin/bash

# K<PERSON>bra Kitchens Deployment Script
# Domain: khobrakitchens.com
# Server IP: ************

echo "🚀 Starting deployment for Khobra Kitchens..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="khobrakitchens.com"
SERVER_IP="************"
PROJECT_DIR="/var/www/html"
NGINX_CONF="/etc/nginx/sites-available/khobrakitchens.com"
BACKUP_DIR="/var/backups/khobrakitchens"

# Functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Create backup directory
print_status "Creating backup directory..."
sudo mkdir -p $BACKUP_DIR
sudo chown $USER:$USER $BACKUP_DIR

# Backup current deployment (if exists)
if [ -d "$PROJECT_DIR/dist" ]; then
    print_status "Creating backup of current deployment..."
    sudo cp -r $PROJECT_DIR/dist $BACKUP_DIR/dist_$(date +%Y%m%d_%H%M%S)
    print_success "Backup created"
fi

# Install dependencies
print_status "Installing dependencies..."
cd $PROJECT_DIR
npm install --legacy-peer-deps
if [ $? -eq 0 ]; then
    print_success "Dependencies installed"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Build the project
print_status "Building the project..."
npm run build
if [ $? -eq 0 ]; then
    print_success "Project built successfully"
else
    print_error "Build failed"
    exit 1
fi

# Set proper permissions
print_status "Setting file permissions..."
sudo chown -R www-data:www-data $PROJECT_DIR/dist
sudo chmod -R 755 $PROJECT_DIR/dist
print_success "Permissions set"

# Copy nginx configuration
print_status "Configuring Nginx..."
if [ -f "nginx.conf" ]; then
    sudo cp nginx.conf $NGINX_CONF
    sudo ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
    print_success "Nginx configuration updated"
else
    print_warning "nginx.conf not found, skipping nginx configuration"
fi

# Test nginx configuration
print_status "Testing Nginx configuration..."
sudo nginx -t
if [ $? -eq 0 ]; then
    print_success "Nginx configuration is valid"
    
    # Reload nginx
    print_status "Reloading Nginx..."
    sudo systemctl reload nginx
    print_success "Nginx reloaded"
else
    print_error "Nginx configuration test failed"
    exit 1
fi

# Create SSL certificate directories (if they don't exist)
print_status "Checking SSL certificate directories..."
sudo mkdir -p /etc/ssl/certs
sudo mkdir -p /etc/ssl/private

# Set up firewall rules (if ufw is available)
if command -v ufw &> /dev/null; then
    print_status "Configuring firewall..."
    sudo ufw allow 'Nginx Full'
    sudo ufw allow ssh
    print_success "Firewall configured"
fi

# Create systemd service for auto-renewal (if using Let's Encrypt)
print_status "Setting up SSL certificate auto-renewal..."
cat << EOF | sudo tee /etc/systemd/system/ssl-renewal.service > /dev/null
[Unit]
Description=SSL Certificate Renewal for Khobra Kitchens
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/bin/certbot renew --quiet --nginx
User=root

[Install]
WantedBy=multi-user.target
EOF

cat << EOF | sudo tee /etc/systemd/system/ssl-renewal.timer > /dev/null
[Unit]
Description=Run SSL Certificate Renewal twice daily
Requires=ssl-renewal.service

[Timer]
OnCalendar=*-*-* 00,12:00:00
RandomizedDelaySec=3600
Persistent=true

[Install]
WantedBy=timers.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable ssl-renewal.timer
sudo systemctl start ssl-renewal.timer

print_success "SSL auto-renewal configured"

# Display deployment information
echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Deployment Summary:"
echo "   Domain: $DOMAIN"
echo "   Server IP: $SERVER_IP"
echo "   Project Directory: $PROJECT_DIR"
echo "   Build Directory: $PROJECT_DIR/dist"
echo ""
echo "🔗 URLs:"
echo "   Website: https://$DOMAIN"
echo "   Admin Panel: https://$DOMAIN/admin"
echo ""
echo "📝 Next Steps:"
echo "   1. Configure SSL certificates if not already done"
echo "   2. Update DNS records to point to $SERVER_IP"
echo "   3. Test the website functionality"
echo "   4. Set up monitoring and backups"
echo ""
echo "🔐 Admin Credentials:"
echo "   Username: admin_khobra_kitchens"
echo "   Password: khobra_admin_2024"
echo "   (Change these in production!)"
echo ""

print_success "Deployment script completed!"
