[Unit]
Description=Khobra Kitchens Services
Documentation=https://khobrakitchens.com
After=network.target
Wants=network.target

[Service]
Type=forking
User=root
WorkingDirectory=/var/www/html
Environment=NODE_ENV=production
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ExecStart=/usr/lib/node_modules/pm2/bin/pm2 start ecosystem.config.cjs --env production
ExecReload=/usr/lib/node_modules/pm2/bin/pm2 reload ecosystem.config.cjs --env production
ExecStop=/usr/lib/node_modules/pm2/bin/pm2 stop all
Restart=on-failure
RestartSec=10
KillMode=mixed
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
