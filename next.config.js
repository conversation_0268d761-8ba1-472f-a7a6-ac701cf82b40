/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable React strict mode
  reactStrictMode: true,

  // Configure images
  images: {
    domains: ['images.unsplash.com', 'localhost'],
    unoptimized: true, // For static export
  },

  // Configure for server deployment
  trailingSlash: true,

  // Configure asset prefix for production
  assetPrefix: process.env.NODE_ENV === 'production' ? '' : '',

  // Configure base path if needed
  basePath: '',

  // Disable x-powered-by header
  poweredByHeader: false,
  
  // Configure webpack for sql.js
  webpack: (config, { isServer }) => {
    // Handle sql.js
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      crypto: false,
    };
    
    // Handle .wasm files for sql.js
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
    };
    
    return config;
  },
  

};

module.exports = nextConfig;
