(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[158],{181:(t,e,i)=>{"use strict";i.d(e,{E:()=>n});var s=i(4232);let n=i(2205).B?s.useLayoutEffect:s.useEffect},1200:(t,e,i)=>{"use strict";i.d(e,{M:()=>n});var s=i(4232);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},2205:(t,e,i)=>{"use strict";i.d(e,{B:()=>s});let s="undefined"!=typeof window},3866:(t,e,i)=>{"use strict";i.d(e,{t:()=>s});let s=(0,i(4232).createContext)(null)},3885:(t,e,i)=>{"use strict";i.d(e,{xQ:()=>r});var s=i(4232),n=i(3866);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{if(t)return a(l)},[t]);let h=(0,s.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,h]:[!0]}},4148:()=>{},5048:(t,e,i)=>{"use strict";i.d(e,{L:()=>s});let s=(0,i(4232).createContext)({})},7042:()=>{},7212:(t,e,i)=>{"use strict";i.d(e,{N:()=>v});var s=i(7876),n=i(4232),r=i(5048),o=i(1200),a=i(181),l=i(3866),h=i(7990),u=i(9751);class d extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,h.s)(t)&&t.offsetWidth||0,s=this.props.sizeRef.current;s.height=e.offsetHeight||0,s.width=e.offsetWidth||0,s.top=e.offsetTop,s.left=e.offsetLeft,s.right=i-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function c({children:t,isPresent:e,anchorX:i,root:r}){let o=(0,n.useId)(),a=(0,n.useRef)(null),l=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,n.useContext)(u.Q);return(0,n.useInsertionEffect)(()=>{let{width:t,height:s,top:n,left:u,right:d}=l.current;if(e||!a.current||!t||!s)return;let c="left"===i?`left: ${u}`:`right: ${d}`;a.current.dataset.motionPopId=o;let p=document.createElement("style");h&&(p.nonce=h);let m=r??document.head;return m.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${s}px !important;
            ${c}px !important;
            top: ${n}px !important;
          }
        `),()=>{m.removeChild(p),m.contains(p)&&m.removeChild(p)}},[e]),(0,s.jsx)(d,{isPresent:e,childRef:a,sizeRef:l,children:n.cloneElement(t,{ref:a})})}let p=({children:t,initial:e,isPresent:i,onExitComplete:r,custom:a,presenceAffectsLayout:h,mode:u,anchorX:d,root:p})=>{let f=(0,o.M)(m),g=(0,n.useId)(),y=!0,v=(0,n.useMemo)(()=>(y=!1,{id:g,initial:e,isPresent:i,custom:a,onExitComplete:t=>{for(let e of(f.set(t,!0),f.values()))if(!e)return;r&&r()},register:t=>(f.set(t,!1),()=>f.delete(t))}),[i,f,r]);return h&&y&&(v={...v}),(0,n.useMemo)(()=>{f.forEach((t,e)=>f.set(e,!1))},[i]),n.useEffect(()=>{i||f.size||!r||r()},[i]),"popLayout"===u&&(t=(0,s.jsx)(c,{isPresent:i,anchorX:d,root:p,children:t})),(0,s.jsx)(l.t.Provider,{value:v,children:t})};function m(){return new Map}var f=i(3885);let g=t=>t.key||"";function y(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}let v=({children:t,custom:e,initial:i=!0,onExitComplete:l,presenceAffectsLayout:h=!0,mode:u="sync",propagate:d=!1,anchorX:c="left",root:m})=>{let[v,x]=(0,f.xQ)(d),T=(0,n.useMemo)(()=>y(t),[t]),w=d&&!v?[]:T.map(g),P=(0,n.useRef)(!0),b=(0,n.useRef)(T),S=(0,o.M)(()=>new Map),[A,M]=(0,n.useState)(T),[E,V]=(0,n.useState)(T);(0,a.E)(()=>{P.current=!1,b.current=T;for(let t=0;t<E.length;t++){let e=g(E[t]);w.includes(e)?S.delete(e):!0!==S.get(e)&&S.set(e,!1)}},[E,w.length,w.join("-")]);let C=[];if(T!==A){let t=[...T];for(let e=0;e<E.length;e++){let i=E[e],s=g(i);w.includes(s)||(t.splice(e,0,i),C.push(i))}return"wait"===u&&C.length&&(t=C),V(y(t)),M(T),null}let{forceRender:D}=(0,n.useContext)(r.L);return(0,s.jsx)(s.Fragment,{children:E.map(t=>{let n=g(t),r=(!d||!!v)&&(T===E||w.includes(n));return(0,s.jsx)(p,{isPresent:r,initial:(!P.current||!!i)&&void 0,custom:e,presenceAffectsLayout:h,mode:u,root:m,onExitComplete:r?void 0:()=>{if(!S.has(n))return;S.set(n,!0);let t=!0;S.forEach(e=>{e||(t=!1)}),t&&(D?.(),V(b.current),d&&x?.(),l&&l())},anchorX:c,children:t},n)})})}},7594:(t,e,i)=>{"use strict";function s(t){return"object"==typeof t&&null!==t}i.d(e,{G:()=>s})},7990:(t,e,i)=>{"use strict";i.d(e,{s:()=>n});var s=i(7594);function n(t){return(0,s.G)(t)&&"offsetHeight"in t}},9315:(t,e,i)=>{"use strict";let s;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function r(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,s){if("function"==typeof e){let[n,o]=r(s);e=e(void 0!==i?i:t.custom,n,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,o]=r(s);e=e(void 0!==i?i:t.custom,n,o)}return e}function a(t,e,i){let s=t.getProps();return o(s,e,void 0!==i?i:s.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>rf});let h=t=>t,u={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,o=d.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,r=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){o.has(e)&&(u.schedule(e),t()),l++,e(a)}let u={schedule:(t,e=!1,r=!1)=>{let a=r&&n?i:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{if(a=t,n){r=!0;return}n=!0,[i,s]=[s,i],i.forEach(h),e&&c.value&&c.value.frameloop[e].push(l),l=0,i.clear(),n=!1,r&&(r=!1,u.process(t))}};return u}(r,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:h,preUpdate:p,update:m,preRender:f,render:g,postRender:y}=o,v=()=>{let r=u.useManualTiming?n.timestamp:performance.now();i=!1,u.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,a.process(n),l.process(n),h.process(n),p.process(n),m.process(n),f.process(n),g.process(n),y.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(v))};return{schedule:d.reduce((e,r)=>{let a=o[r];return e[r]=(e,r=!1,o=!1)=>(!i&&(i=!0,s=!0,n.isProcessing||t(v)),a.schedule(e,r,o)),e},{}),cancel:t=>{for(let e=0;e<d.length;e++)o[d[e]].cancel(t)},state:n,steps:o}}let{schedule:m,cancel:f,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:h,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),T=new Set(["width","height","top","left","right","bottom",...v]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function P(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class b{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>P(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function S(){s=void 0}let A={now:()=>(void 0===s&&A.set(g.isProcessing||u.useManualTiming?g.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(S)}},M={current:void 0};class E{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=A.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new b);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function V(t,e){return new E(t,e)}let C=t=>Array.isArray(t),D=t=>!!(t&&t.getVelocity);function k(t,e){let i=t.getValue("willChange");if(D(i)&&i.add)return i.add(e);if(!i&&u.WillChange){let i=new u.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let R=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),j="data-"+R("framerAppearId"),L=(t,e)=>i=>e(t(i)),F=(...t)=>t.reduce(L),B=(t,e,i)=>i>e?e:i<t?t:i,O=t=>1e3*t,I={layout:0,mainThread:0,waapi:0},U=()=>{},$=()=>{},N=t=>e=>"string"==typeof e&&e.startsWith(t),W=N("--"),z=N("var(--"),Y=t=>!!z(t)&&X.test(t.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,H={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},K={...H,transform:t=>B(0,1,t)},q={...H,default:1},G=t=>Math.round(1e5*t)/1e5,_=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Z=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Q=(t,e)=>i=>!!("string"==typeof i&&Z.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),J=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,o,a]=s.match(_);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tt={...H,transform:t=>Math.round(B(0,255,t))},te={test:Q("rgb","red"),parse:J("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tt.transform(t)+", "+tt.transform(e)+", "+tt.transform(i)+", "+G(K.transform(s))+")"},ti={test:Q("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:te.transform},ts=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tn=ts("deg"),tr=ts("%"),to=ts("px"),ta=ts("vh"),tl=ts("vw"),th={...tr,parse:t=>tr.parse(t)/100,transform:t=>tr.transform(100*t)},tu={test:Q("hsl","hue"),parse:J("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tr.transform(G(e))+", "+tr.transform(G(i))+", "+G(K.transform(s))+")"},td={test:t=>te.test(t)||ti.test(t)||tu.test(t),parse:t=>te.test(t)?te.parse(t):tu.test(t)?tu.parse(t):ti.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?te.transform(t):tu.transform(t),getAnimatableNone:t=>{let e=td.parse(t);return e.alpha=0,td.transform(e)}},tc=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tp="number",tm="color",tf=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tg(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,o=e.replace(tf,t=>(td.test(t)?(s.color.push(r),n.push(tm),i.push(td.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tp),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:o,indexes:s,types:n}}function ty(t){return tg(t).values}function tv(t){let{split:e,types:i}=tg(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tp?n+=G(t[r]):e===tm?n+=td.transform(t[r]):n+=t[r]}return n}}let tx=t=>"number"==typeof t?0:td.test(t)?td.getAnimatableNone(t):t,tT={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(_)?.length||0)+(t.match(tc)?.length||0)>0},parse:ty,createTransformer:tv,getAnimatableNone:function(t){let e=ty(t);return tv(t)(e.map(tx))}};function tw(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tP(t,e){return i=>i>0?e:t}let tb=(t,e,i)=>t+(e-t)*i,tS=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},tA=[ti,te,tu];function tM(t){let e=tA.find(e=>e.test(t));if(U(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tu&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;n=tw(a,s,t+1/3),r=tw(a,s,t),o=tw(a,s,t-1/3)}else n=r=o=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(i)),i}let tE=(t,e)=>{let i=tM(t),s=tM(e);if(!i||!s)return tP(t,e);let n={...i};return t=>(n.red=tS(i.red,s.red,t),n.green=tS(i.green,s.green,t),n.blue=tS(i.blue,s.blue,t),n.alpha=tb(i.alpha,s.alpha,t),te.transform(n))},tV=new Set(["none","hidden"]);function tC(t,e){return i=>tb(t,e,i)}function tD(t){return"number"==typeof t?tC:"string"==typeof t?Y(t)?tP:td.test(t)?tE:tj:Array.isArray(t)?tk:"object"==typeof t?td.test(t)?tE:tR:tP}function tk(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>tD(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function tR(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=tD(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let tj=(t,e)=>{let i=tT.createTransformer(e),s=tg(t),n=tg(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?tV.has(t)&&!n.values.length||tV.has(e)&&!s.values.length?function(t,e){return tV.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):F(tk(function(t,e){let i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let r=e.types[n],o=t.indexes[r][s[r]],a=t.values[o]??0;i[n]=a,s[r]++}return i}(s,n),n.values),i):(U(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tP(t,e))};function tL(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tb(t,e,i):tD(t)(t,e)}let tF=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>g.isProcessing?g.timestamp:A.now()}},tB=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`};function tO(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tI(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let tU={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function t$(t,e){return t*Math.sqrt(1-e*e)}let tN=["duration","bounce"],tW=["stiffness","damping","mass"];function tz(t,e){return e.some(e=>void 0!==t[e])}function tY(t=tU.visualDuration,e=tU.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tU.velocity,stiffness:tU.stiffness,damping:tU.damping,mass:tU.mass,isResolvedFromDuration:!1,...t};if(!tz(t,tW)&&tz(t,tN))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:tU.mass,stiffness:s,damping:n}}else{let i=function({duration:t=tU.duration,bounce:e=tU.bounce,velocity:i=tU.velocity,mass:s=tU.mass}){let n,r;U(t<=O(tU.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=B(tU.minDamping,tU.maxDamping,o),t=B(tU.minDuration,tU.maxDuration,t/1e3),o<1?(n=e=>{let s=e*o,n=s*t;return .001-(s-i)/t$(e,o)*Math.exp(-n)},r=e=>{let s=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=t$(Math.pow(e,2),o);return(s*i+i-r)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=O(t),isNaN(a))return{stiffness:tU.stiffness,damping:tU.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:tU.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-((s.velocity||0)/1e3)}),f=p||0,g=u/(2*Math.sqrt(h*d)),y=a-o,v=Math.sqrt(h/d)/1e3,x=5>Math.abs(y);if(n||(n=x?tU.restSpeed.granular:tU.restSpeed.default),r||(r=x?tU.restDelta.granular:tU.restDelta.default),g<1){let t=t$(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),s=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(s)+t*y*Math.cosh(s))/t}}let T={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0===t?f:0;g<1&&(s=0===t?O(f):tI(i,t,e));let o=Math.abs(a-e)<=r;l.done=Math.abs(s)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tO(T),2e4),e=tB(e=>T.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return T}function tX({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:h=.5,restSpeed:u}){let d,c,p=t[0],m={done:!1,value:p},f=i*e,g=p+f,y=void 0===o?g:o(g);y!==g&&(f=y-p);let v=t=>-f*Math.exp(-t/s),x=t=>y+v(t),T=t=>{let e=v(t),i=x(t);m.done=Math.abs(e)<=h,m.value=m.done?y:i},w=t=>{let e;if(e=m.value,void 0!==a&&e<a||void 0!==l&&e>l){var i;d=t,c=tY({keyframes:[m.value,(i=m.value,void 0===a?l:void 0===l||Math.abs(a-i)<Math.abs(l-i)?a:l)],velocity:tI(x,t,m.value),damping:n,stiffness:r,restDelta:h,restSpeed:u})}};return w(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),w(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}tY.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),n=Math.min(tO(s),2e4);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:n/1e3}}(t,100,tY);return t.ease=e.ease,t.duration=O(e.duration),t.type="keyframes",t};let tH=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tK(t,e,i,s){return t===e&&i===s?h:n=>0===n||1===n?n:tH(function(t,e,i,s,n){let r,o,a=0;do(r=tH(o=e+(i-e)/2,s,n)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o}(n,0,1,t,i),e,s)}let tq=tK(.42,0,1,1),tG=tK(0,0,.58,1),t_=tK(.42,0,.58,1),tZ=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tQ=t=>e=>1-t(1-e),tJ=tK(.33,1.53,.69,.99),t0=tQ(tJ),t1=tZ(t0),t2=t=>(t*=2)<1?.5*t0(t):.5*(2-Math.pow(2,-10*(t-1))),t5=t=>1-Math.sin(Math.acos(t)),t3=tQ(t5),t9=tZ(t5),t4=t=>Array.isArray(t)&&"number"==typeof t[0],t6={linear:h,easeIn:tq,easeInOut:t_,easeOut:tG,circIn:t5,circInOut:t9,circOut:t3,backIn:t0,backInOut:t1,backOut:tJ,anticipate:t2},t8=t=>{if(t4(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return tK(e,i,s,n)}return"string"==typeof t?($(void 0!==t6[t],`Invalid easing type '${t}'`),t6[t]):t},t7=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s};function et({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=Array.isArray(s)&&"number"!=typeof s[0]?s.map(t8):t8(s),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if($(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let s=[],n=i||u.mix||tL,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=F(Array.isArray(e)?e[i]||h:e,r)),s.push(r)}return s}(e,s,n),l=a.length,d=i=>{if(o&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=t7(t[s],t[s+1],i);return a[s](n)};return i?e=>d(B(t[0],t[r-1],e)):d}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=t7(0,e,s);t.push(tb(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||t_).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ee=t=>null!==t;function ei(t,{repeat:e,repeatType:i="loop"},s,n=1){let r=t.filter(ee),o=n<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return o&&void 0!==s?s:r[o]}let es={decay:tX,inertia:tX,tween:et,keyframes:et,spring:tY};function en(t){"string"==typeof t.type&&(t.type=es[t.type])}class er{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let eo=t=>t/100;class ea extends er{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},I.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;en(t);let{type:e=et,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:r=0}=t,{keyframes:o}=t,a=e||et;a!==et&&"number"!=typeof o[0]&&(this.mixKeyframes=F(eo,tL(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=tO(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let v=this.currentTime,x=i;if(u){let t=Math.min(this.currentTime,s)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=r)),v=B(0,1,i)*o}let T=y?{done:!1,value:h[0]}:x.next(v);n&&(T.value=n(T.value));let{done:w}=T;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&p!==tX&&(T.value=ei(h,this.options,f,this.speed)),m&&m(T.value),P&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(t){t=O(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:t=tF,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,I.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let el=t=>180*t/Math.PI,eh=t=>ed(el(Math.atan2(t[1],t[0]))),eu={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:eh,rotateZ:eh,skewX:t=>el(Math.atan(t[1])),skewY:t=>el(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ed=t=>((t%=360)<0&&(t+=360),t),ec=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ep=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),em={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ec,scaleY:ep,scale:t=>(ec(t)+ep(t))/2,rotateX:t=>ed(el(Math.atan2(t[6],t[5]))),rotateY:t=>ed(el(Math.atan2(-t[2],t[0]))),rotateZ:eh,rotate:eh,skewX:t=>el(Math.atan(t[4])),skewY:t=>el(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ef(t){return+!!t.includes("scale")}function eg(t,e){let i,s;if(!t||"none"===t)return ef(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=em,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eu,s=e}if(!s)return ef(e);let r=i[e],o=s[1].split(",").map(ey);return"function"==typeof r?r(o):o[r]}function ey(t){return parseFloat(t.trim())}let ev=t=>t===H||t===to,ex=new Set(["x","y","z"]),eT=v.filter(t=>!ex.has(t)),ew={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eg(e,"x"),y:(t,{transform:e})=>eg(e,"y")};ew.translateX=ew.x,ew.translateY=ew.y;let eP=new Set,eb=!1,eS=!1,eA=!1;function eM(){if(eS){let t=Array.from(eP).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eT.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eS=!1,eb=!1,eP.forEach(t=>t.complete(eA)),eP.clear()}function eE(){eP.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eS=!0)})}class eV{constructor(t,e,i,s,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(eP.add(this),eb||(eb=!0,m.read(eE),m.resolveKeyframes(eM))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eP.delete(this)}cancel(){"scheduled"===this.state&&(eP.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}function eC(t){let e;return()=>(void 0===e&&(e=t()),e)}let eD=eC(()=>void 0!==window.ScrollTimeline),ek={},eR=function(t,e){let i=eC(t);return()=>ek[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),ej=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,eL={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ej([0,.65,.55,1]),circOut:ej([.55,0,1,.45]),backIn:ej([.31,.01,.66,-.59]),backOut:ej([.33,1.53,.69,.99])};function eF(t){return"function"==typeof t&&"applyToOptions"in t}class eB extends er{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=t,$("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eF(t)&&eR()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eR()?tB(e,i):"ease-out":t4(e)?ej(e):Array.isArray(e)?e.map(e=>t(e,i)||eL.easeOut):eL[e]}(a,n);Array.isArray(d)&&(u.easing=d),c.value&&I.waapi++;let p={delay:s,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};h&&(p.pseudoElement=h);let m=t.animate(u,p);return c.value&&m.finished.finally(()=>{I.waapi--}),m}(e,i,s,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=ei(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e.startsWith("--")?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(t){this.finishedTime=null,this.animation.currentTime=O(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eD())?(this.animation.timeline=t,h):e(this)}}let eO={anticipate:t2,backInOut:t1,circInOut:t9};class eI extends eB{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eO&&(t.ease=eO[t.ease])}(t),en(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:n,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ea({...r,autoplay:!1}),a=O(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eU=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.test(t)||"0"===t)&&!t.startsWith("url("));var e$,eN,eW=i(7990);let ez=new Set(["opacity","clipPath","filter","transform"]),eY=eC(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eX extends er{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",keyframes:o,name:a,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.now();let d={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,name:a,motionValue:l,element:h,...u},c=h?.KeyframeResolver||eV;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:n,type:r,velocity:o,delay:a,isHandoff:l,onUpdate:d}=i;this.resolvedAt=A.now(),!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=eU(n,e),a=eU(r,e);return U(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eF(i))&&s)}(t,n,r,o)&&((u.instantAnimations||!a)&&d?.(ei(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let c={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!(0,eW.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eY()&&i&&ez.has(i)&&("transform"!==i||!l)&&!a&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}(c)?new eI({...c,element:c.motionValue.owner.current}):new ea(c);p.finished.then(()=>this.notifyFinished()).catch(h),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eA=!0,eE(),eM(),eA=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let eH=t=>null!==t,eK={type:"spring",stiffness:500,damping:25,restSpeed:10},eq={type:"keyframes",duration:.8},eG={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e_=(t,e,i,s={},n,r)=>o=>{let a=l(s,t)||{},h=a.delay||s.delay||0,{elapsed:d=0}=s;d-=O(h);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-d,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(a)&&Object.assign(c,((t,{keyframes:e})=>e.length>2?eq:x.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:eK:eG)(t,c)),c.duration&&(c.duration=O(c.duration)),c.repeatDelay&&(c.repeatDelay=O(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(p=!0)),(u.instantAnimations||u.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,p&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(eH),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[r]}(c.keyframes,a);if(void 0!==t)return void m.update(()=>{c.onUpdate(t),c.onComplete()})}return a.isSync?new ea(c):new eX(c)};function eZ(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...h}=e;s&&(r=s);let u=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in h){let s=t.getValue(e,t.latestValues[e]??null),n=h[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(d,e))continue;let o={delay:i,...l(r||{},e)},a=s.get();if(void 0!==a&&!s.isAnimating&&!Array.isArray(n)&&n===a&&!o.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[j];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(o.startTime=t,c=!0)}}k(t,e),s.start(e_(e,s,n,t.shouldReduceMotion&&T.has(e)?{type:!1}:o,t,c));let p=s.animation;p&&u.push(p)}return o&&Promise.all(u).then(()=>{m.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=a(t,e)||{};for(let e in n={...n,...i}){var r;let i=C(r=n[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,V(i))}}(t,o)})}),u}function eQ(t,e,i={}){let s=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let r=s?()=>Promise.all(eZ(t,s,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(eJ).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(eQ(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,r+s,o,a,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([r(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[r,o]:[o,r];return t().then(()=>e())}}function eJ(t,e){return t.sortNodePosition(e)}function e0(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function e1(t){return"string"==typeof t||Array.isArray(t)}let e2=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],e5=["initial",...e2],e3=e5.length,e9=[...e2].reverse(),e4=e2.length;function e6(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function e8(){return{animate:e6(!0),whileInView:e6(),whileHover:e6(),whileTap:e6(),whileDrag:e6(),whileFocus:e6(),exit:e6()}}class e7{constructor(t){this.isMounted=!1,this.node=t}update(){}}class it extends e7{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>eQ(t,e,i)));else if("string"==typeof e)s=eQ(t,e,i);else{let n="function"==typeof e?a(t,e,i.custom):e;s=Promise.all(eZ(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=e8(),s=!0,r=e=>(i,s)=>{let n=a(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function o(o){let{props:l}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<e3;t++){let s=e5[t],n=e.props[s];(e1(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},u=[],d=new Set,c={},p=1/0;for(let e=0;e<e4;e++){var m,f;let a=e9[e],g=i[a],y=void 0!==l[a]?l[a]:h[a],v=e1(y),x=a===o?g.isActive:null;!1===x&&(p=e);let T=y===h[a]&&y!==l[a]&&v;if(T&&s&&t.manuallyAnimateOnMount&&(T=!1),g.protectedKeys={...c},!g.isActive&&null===x||!y&&!g.prevProp||n(y)||"boolean"==typeof y)continue;let w=(m=g.prevProp,"string"==typeof(f=y)?f!==m:!!Array.isArray(f)&&!e0(f,m)),P=w||a===o&&g.isActive&&!T&&v||e>p&&v,b=!1,S=Array.isArray(y)?y:[y],A=S.reduce(r(a),{});!1===x&&(A={});let{prevResolvedValues:M={}}=g,E={...M,...A},V=e=>{P=!0,d.has(e)&&(b=!0,d.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in E){let e=A[t],i=M[t];if(!c.hasOwnProperty(t))(C(e)&&C(i)?e0(e,i):e===i)?void 0!==e&&d.has(t)?V(t):g.protectedKeys[t]=!0:null!=e?V(t):d.add(t)}g.prevProp=y,g.prevResolvedValues=A,g.isActive&&(c={...c,...A}),s&&t.blockInitialAnimation&&(P=!1);let D=!(T&&w)||b;P&&D&&u.push(...S.map(t=>({animation:t,options:{type:a}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),u.push({animation:e})}let g=!!u.length;return s&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(u):Promise.resolve()}return{animateChanges:o,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=o(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=e8(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ie=0;class ii extends e7{constructor(){super(...arguments),this.id=ie++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let is={x:!1,y:!1};function ir(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let io=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ia(t){return{point:{x:t.pageX,y:t.pageY}}}function il(t,e,i,s){return ir(t,e,t=>io(t)&&i(t,ia(t)),s)}function ih({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iu(t){return t.max-t.min}function id(t,e,i,s=.5){t.origin=s,t.originPoint=tb(e.min,e.max,t.origin),t.scale=iu(i)/iu(e),t.translate=tb(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function ic(t,e,i,s){id(t.x,e.x,i.x,s?s.originX:void 0),id(t.y,e.y,i.y,s?s.originY:void 0)}function ip(t,e,i){t.min=i.min+e.min,t.max=t.min+iu(e)}function im(t,e,i){t.min=e.min-i.min,t.max=t.min+iu(e)}function ig(t,e,i){im(t.x,e.x,i.x),im(t.y,e.y,i.y)}let iy=()=>({translate:0,scale:1,origin:0,originPoint:0}),iv=()=>({x:iy(),y:iy()}),ix=()=>({min:0,max:0}),iT=()=>({x:ix(),y:ix()});function iw(t){return[t("x"),t("y")]}function iP(t){return void 0===t||1===t}function ib({scale:t,scaleX:e,scaleY:i}){return!iP(t)||!iP(e)||!iP(i)}function iS(t){return ib(t)||iA(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iA(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iM(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function iE(t,e=0,i=1,s,n){t.min=iM(t.min,e,i,s,n),t.max=iM(t.max,e,i,s,n)}function iV(t,{x:e,y:i}){iE(t.x,e.translate,e.scale,e.originPoint),iE(t.y,i.translate,i.scale,i.originPoint)}function iC(t,e){t.min=t.min+e,t.max=t.max+e}function iD(t,e,i,s,n=.5){let r=tb(t.min,t.max,n);iE(t,e,i,r,s)}function ik(t,e){iD(t.x,e.x,e.scaleX,e.scale,e.originX),iD(t.y,e.y,e.scaleY,e.scale,e.originY)}function iR(t,e){return ih(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let ij=({current:t})=>t?t.ownerDocument.defaultView:null;function iL(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iF=(t,e)=>Math.abs(t-e);class iB{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iU(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iF(t.x,e.x)**2+iF(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=g;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iO(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=iU("pointercancel"===t.type?this.lastMoveEventInfo:iO(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!io(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=iO(ia(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=g;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iU(r,this.history)),this.removeListeners=F(il(this.contextWindow,"pointermove",this.handlePointerMove),il(this.contextWindow,"pointerup",this.handlePointerUp),il(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iO(t,e){return e?{point:e(t.point)}:t}function iI(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iU({point:t},e){return{point:t,delta:iI(t,i$(e)),offset:iI(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=i$(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>O(.1)));)i--;if(!s)return{x:0,y:0};let r=(n.timestamp-s.timestamp)/1e3;if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function i$(t){return t[t.length-1]}function iN(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iW(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function iz(t,e,i){return{min:iY(t,e),max:iY(t,i)}}function iY(t,e){return"number"==typeof t?t:t[e]||0}let iX=new WeakMap;class iH{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iT(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iB(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ia(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(is[t])return null;else return is[t]=!0,()=>{is[t]=!1};return is.x||is.y?null:(is.x=is.y=!0,()=>{is.x=is.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iw(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tr.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iu(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&m.postRender(()=>n(t,e)),k(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iw(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:ij(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&m.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!iK(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?tb(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?tb(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&iL(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:iN(t.x,i,n),y:iN(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iz(t,"left","right"),y:iz(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iw(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iL(e))return!1;let s=e.current;$(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=iR(t,i),{scroll:n}=e;return n&&(iC(s.x,n.offset.x),iC(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:iW(t.x,r.x),y:iW(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ih(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iw(o=>{if(!iK(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,h)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return k(this.visualElement,t),i.start(e_(t,i,0,e,this.visualElement,!1))}stopAnimation(){iw(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iw(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iw(e=>{let{drag:i}=this.getProps();if(!iK(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-tb(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iL(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iw(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iu(t),n=iu(e);return n>s?i=t7(e.min,e.max-s,t.min):s>n&&(i=t7(t.min,t.max-n,e.min)),B(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iw(e=>{if(!iK(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(tb(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;iX.set(this.visualElement,this);let t=il(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iL(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let n=ir(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iw(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function iK(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class iq extends e7{constructor(t){super(t),this.removeGroupControls=h,this.removeListeners=h,this.controls=new iH(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||h}unmount(){this.removeGroupControls(),this.removeListeners()}}let iG=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i_ extends e7{constructor(){super(...arguments),this.removePointerDownListener=h}onPointerDown(t){this.session=new iB(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ij(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:iG(t),onStart:iG(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&m.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=il(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var iZ=i(7876);let{schedule:iQ}=p(queueMicrotask,!1);var iJ=i(4232),i0=i(3885),i1=i(5048);let i2=(0,iJ.createContext)({}),i5={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function i3(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let i9={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!to.test(t))return t;else t=parseFloat(t);let i=i3(t,e.target.x),s=i3(t,e.target.y);return`${i}% ${s}%`}},i4={};class i6 extends iJ.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;for(let t in i7)i4[t]=i7[t],W(t)&&(i4[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),i5.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,{projection:r}=i;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||m.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),iQ.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function i8(t){let[e,i]=(0,i0.xQ)(),s=(0,iJ.useContext)(i1.L);return(0,iZ.jsx)(i6,{...t,layoutGroup:s,switchLayoutGroup:(0,iJ.useContext)(i2),isPresent:e,safeToRemove:i})}let i7={borderRadius:{...i9,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:i9,borderTopRightRadius:i9,borderBottomLeftRadius:i9,borderBottomRightRadius:i9,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tT.parse(t);if(s.length>5)return t;let n=tT.createTransformer(t),r=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=tb(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}};var st=i(7594);function se(t){return(0,st.G)(t)&&"ownerSVGElement"in t}let si=(t,e)=>t.depth-e.depth;class ss{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){P(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(si),this.isDirty=!1,this.children.forEach(t)}}function sn(t){return D(t)?t.get():t}let sr=["TopLeft","TopRight","BottomLeft","BottomRight"],so=sr.length,sa=t=>"string"==typeof t?parseFloat(t):t,sl=t=>"number"==typeof t||to.test(t);function sh(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let su=sc(0,.5,t3),sd=sc(.5,.95,h);function sc(t,e,i){return s=>s<t?0:s>e?1:i(t7(t,e,s))}function sp(t,e){t.min=e.min,t.max=e.max}function sm(t,e){sp(t.x,e.x),sp(t.y,e.y)}function sf(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sg(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sy(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if(tr.test(e)&&(e=parseFloat(e),e=tb(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tb(r.min,r.max,s);t===r&&(a-=e),t.min=sg(t.min,e,i,a,n),t.max=sg(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let sv=["x","scaleX","originX"],sx=["y","scaleY","originY"];function sT(t,e,i,s){sy(t.x,e,sv,i?i.x:void 0,s?s.x:void 0),sy(t.y,e,sx,i?i.y:void 0,s?s.y:void 0)}function sw(t){return 0===t.translate&&1===t.scale}function sP(t){return sw(t.x)&&sw(t.y)}function sb(t,e){return t.min===e.min&&t.max===e.max}function sS(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sA(t,e){return sS(t.x,e.x)&&sS(t.y,e.y)}function sM(t){return iu(t.x)/iu(t.y)}function sE(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sV{constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(P(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sC={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sD=["","X","Y","Z"],sk={visibility:"hidden"},sR=0;function sj(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sL({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=sR++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,c.value&&(sC.nodes=sC.calculatedTargetDeltas=sC.calculatedProjections=0),this.nodes.forEach(sO),this.nodes.forEach(sY),this.nodes.forEach(sX),this.nodes.forEach(sI),c.addProjectionMetrics&&c.addProjectionMetrics(sC)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new ss)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new b),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=se(e)&&!(se(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.now(),s=({timestamp:e})=>{let n=e-i;n>=250&&(f(s),t(n-250))};return m.setup(s,!0),()=>f(s)}(s,250),i5.hasAnimatedSinceResize&&(i5.hasAnimatedSinceResize=!1,this.nodes.forEach(sz))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||sZ,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),h=!this.targetLayout||!sA(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(r,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||sz(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(sH),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[j];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",m,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s$);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(sN);this.isUpdating||this.nodes.forEach(sN),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(sW),this.nodes.forEach(sF),this.nodes.forEach(sB),this.clearAllSnapshots();let t=A.now();g.delta=B(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,iQ.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sU),this.sharedNodes.forEach(sK)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iu(this.snapshot.measuredBox.x)||iu(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iT(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sP(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||iS(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),s0((e=s).x),s0(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iT();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(s2))){let{scroll:t}=this.root;t&&(iC(e.x,t.offset.x),iC(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iT();if(sm(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sm(e,t),iC(e.x,n.offset.x),iC(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iT();sm(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&ik(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iS(s.latestValues)&&ik(i,s.latestValues)}return iS(this.latestValues)&&ik(i,this.latestValues),i}removeTransform(t){let e=iT();sm(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iS(i.latestValues))continue;ib(i.latestValues)&&i.updateSnapshot();let s=iT();sm(s,i.measurePageBox()),sT(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iS(this.latestValues)&&sT(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iT(),this.relativeTargetOrigin=iT(),ig(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sm(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iT(),this.targetWithTransforms=iT()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,o,a;this.forceRelativeParentToResolveTarget(),r=this.target,o=this.relativeTarget,a=this.relativeParent.target,ip(r.x,o.x,a.x),ip(r.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sm(this.target,this.layout.layoutBox),iV(this.target,this.targetDelta)):sm(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iT(),this.relativeTargetOrigin=iT(),ig(this.relativeTargetOrigin,this.target,t.target),sm(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}c.value&&sC.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ib(this.parent.latestValues)||iA(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;sm(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,s=!1){let n,r,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&ik(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iV(t,r)),s&&iS(n.latestValues)&&ik(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iT());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sf(this.prevProjectionDelta.x,this.projectionDelta.x),sf(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ic(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===r&&this.treeScale.y===o&&sE(this.projectionDelta.x,this.prevProjectionDelta.x)&&sE(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),c.value&&sC.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iv(),this.projectionDelta=iv(),this.projectionDeltaWithTransform=iv()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=iv();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iT(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(s_));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(sq(o.x,t.x,s),sq(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,g;ig(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=s,sG(p.x,m.x,f.x,g),sG(p.y,m.y,f.y,g),i&&(h=this.relativeTarget,c=i,sb(h.x,c.x)&&sb(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iT()),sm(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=tb(0,i.opacity??1,su(s)),t.opacityExit=tb(e.opacity??1,0,sd(s))):r&&(t.opacity=tb(e.opacity??1,i.opacity??1,s));for(let n=0;n<so;n++){let r=`border${sr[n]}Radius`,o=sh(e,r),a=sh(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sl(o)===sl(a)?(t[r]=Math.max(tb(sa(o),sa(a),s),0),(tr.test(a)||tr.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=tb(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{i5.hasAnimatedSinceResize=!0,I.layout++,this.motionValue||(this.motionValue=V(0)),this.currentAnimation=function(t,e,i){let s=D(t)?t:V(t);return s.start(e_("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{I.layout--},onComplete:()=>{I.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&s1(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iT();let e=iu(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iu(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sm(e,i),ik(e,n),ic(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sV),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&sj("z",t,s,this.animationValues);for(let e=0;e<sD.length;e++)sj(`rotate${sD[e]}`,t,s,this.animationValues),sj(`skew${sD[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sk;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=sn(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sn(t?.pointerEvents)||""),this.hasProjected&&!iS(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=i?.z||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:r,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*o.origin}% 0`,s.animationValues?e.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,i4){if(void 0===n[t])continue;let{correct:i,applyTo:r,isCSSVariable:o}=i4[t],a="none"===e.transform?n[t]:i(n[t],s);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=s===this?sn(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(s$),this.root.sharedNodes.clear()}}}function sF(t){t.updateLayout()}function sB(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?iw(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=iu(s);s.min=i[t].min,s.max=s.min+n}):s1(n,e.layoutBox,i)&&iw(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],o=iu(i[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=iv();ic(o,i,e.layoutBox);let a=iv();r?ic(a,t.applyTransform(s,!0),e.measuredBox):ic(a,i,e.layoutBox);let l=!sP(o),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=iT();ig(o,e.layoutBox,n.layoutBox);let a=iT();ig(a,i,r.layoutBox),sA(o,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sO(t){c.value&&sC.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sI(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sU(t){t.clearSnapshot()}function s$(t){t.clearMeasurements()}function sN(t){t.isLayoutDirty=!1}function sW(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function sz(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function sY(t){t.resolveTargetDelta()}function sX(t){t.calcProjection()}function sH(t){t.resetSkewAndRotation()}function sK(t){t.removeLeadSnapshot()}function sq(t,e,i){t.translate=tb(e.translate,0,i),t.scale=tb(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function sG(t,e,i,s){t.min=tb(e.min,i.min,s),t.max=tb(e.max,i.max,s)}function s_(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let sZ={duration:.45,ease:[.4,0,.1,1]},sQ=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),sJ=sQ("applewebkit/")&&!sQ("chrome/")?Math.round:h;function s0(t){t.min=sJ(t.min),t.max=sJ(t.max)}function s1(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sM(e)-sM(i)))}function s2(t){return t!==t.root&&t.scroll?.wasRoot}let s5=sL({attachResizeListener:(t,e)=>ir(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),s3={current:void 0},s9=sL({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!s3.current){let t=new s5({});t.mount(window),t.setOptions({layoutScroll:!0}),s3.current=t}return s3.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var s4=i(373);function s6(t,e){let i=(0,s4.K)(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function s8(t){return!("touch"===t.pointerType||is.x||is.y)}function s7(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&m.postRender(()=>n(e,ia(e)))}class nt extends e7{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=s6(t,i),o=t=>{if(!s8(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{s8(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,(t,e)=>(s7(this.node,e,"Start"),t=>s7(this.node,t,"End"))))}unmount(){}}class ne extends e7{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=F(ir(this.node.current,"focus",()=>this.onFocus()),ir(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ni=(t,e)=>!!e&&(t===e||ni(t,e.parentElement)),ns=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nn=new WeakSet;function nr(t){return e=>{"Enter"===e.key&&t(e)}}function no(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function na(t){return io(t)&&!(is.x||is.y)}function nl(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&m.postRender(()=>n(e,ia(e)))}class nh extends e7{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=s6(t,i),o=t=>{let s=t.currentTarget;if(!na(t))return;nn.add(s);let r=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),nn.has(s)&&nn.delete(s),na(t)&&"function"==typeof r&&r(t,{success:e})},a=t=>{o(t,s===window||s===document||i.useGlobalTarget||ni(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),(0,eW.s)(t))&&(t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let s=nr(()=>{if(nn.has(i))return;no(i,"down");let t=nr(()=>{no(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>no(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)})(t,n)),ns.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(nl(this.node,e,"Start"),(t,{success:e})=>nl(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nu=new WeakMap,nd=new WeakMap,nc=t=>{let e=nu.get(t.target);e&&e(t)},np=t=>{t.forEach(nc)},nm={some:0,all:1};class nf extends e7{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:nm[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;nd.has(i)||nd.set(i,{});let s=nd.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(np,{root:t,...e})),s[n]}(e);return nu.set(t,i),s.observe(t),()=>{nu.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let ng=(0,iJ.createContext)({strict:!1});var ny=i(9751);let nv=(0,iJ.createContext)({});function nx(t){return n(t.animate)||e5.some(e=>e1(t[e]))}function nT(t){return!!(nx(t)||t.variants)}function nw(t){return Array.isArray(t)?t.join(" "):t}var nP=i(2205);let nb={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nS={};for(let t in nb)nS[t]={isEnabled:e=>nb[t].some(t=>!!e[t])};let nA=Symbol.for("motionComponentSymbol");var nM=i(3866),nE=i(181);function nV(t,{layout:e,layoutId:i}){return x.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!i4[t]||"opacity"===t)}let nC=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nD={...H,transform:Math.round},nk={borderWidth:to,borderTopWidth:to,borderRightWidth:to,borderBottomWidth:to,borderLeftWidth:to,borderRadius:to,radius:to,borderTopLeftRadius:to,borderTopRightRadius:to,borderBottomRightRadius:to,borderBottomLeftRadius:to,width:to,maxWidth:to,height:to,maxHeight:to,top:to,right:to,bottom:to,left:to,padding:to,paddingTop:to,paddingRight:to,paddingBottom:to,paddingLeft:to,margin:to,marginTop:to,marginRight:to,marginBottom:to,marginLeft:to,backgroundPositionX:to,backgroundPositionY:to,rotate:tn,rotateX:tn,rotateY:tn,rotateZ:tn,scale:q,scaleX:q,scaleY:q,scaleZ:q,skew:tn,skewX:tn,skewY:tn,distance:to,translateX:to,translateY:to,translateZ:to,x:to,y:to,z:to,perspective:to,transformPerspective:to,opacity:K,originX:th,originY:th,originZ:to,zIndex:nD,fillOpacity:K,strokeOpacity:K,numOctaves:nD},nR={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nj=v.length;function nL(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(x.has(t)){o=!0;continue}if(W(t)){n[t]=i;continue}{let e=nC(i,nk[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<nj;r++){let o=v[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=nC(a,nk[o]);if(!l){n=!1;let e=nR[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nF=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nB(t,e,i){for(let s in e)D(e[s])||nV(s,i)||(t[s]=e[s])}let nO={offset:"stroke-dashoffset",array:"stroke-dasharray"},nI={offset:"strokeDashoffset",array:"strokeDasharray"};function nU(t,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:r=1,pathOffset:o=0,...a},l,h,u){if(nL(t,a,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==s&&(d.scale=s),void 0!==n&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?nO:nI;t[r.offset]=to.transform(-s);let o=to.transform(e),a=to.transform(i);t[r.array]=`${o} ${a}`}(d,n,r,o,!1)}let n$=()=>({...nF(),attrs:{}}),nN=t=>"string"==typeof t&&"svg"===t.toLowerCase(),nW=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nz(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nW.has(t)}let nY=t=>!nz(t);try{!function(t){"function"==typeof t&&(nY=e=>e.startsWith("on")?!nz(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let nX=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nH(t){if("string"!=typeof t||t.includes("-"));else if(nX.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nK=i(1200);let nq=t=>(e,i)=>{let s=(0,iJ.useContext)(nv),r=(0,iJ.useContext)(nM.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,r){return{latestValues:function(t,e,i,s){let r={},a=s(t,{});for(let t in a)r[t]=sn(a[t]);let{initial:l,animate:h}=t,u=nx(t),d=nT(t);e&&d&&!u&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===h&&(h=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?h:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=o(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,s,r,t),renderState:e()}})(t,e,s,r);return i?a():(0,nK.M)(a)};function nG(t,e,i){let{style:s}=t,n={};for(let r in s)(D(s[r])||e.style&&D(e.style[r])||nV(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}let n_={useVisualState:nq({scrapeMotionValuesFromProps:nG,createRenderState:nF})};function nZ(t,e,i){let s=nG(t,e,i);for(let i in t)(D(t[i])||D(e[i]))&&(s[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let nQ={useVisualState:nq({scrapeMotionValuesFromProps:nZ,createRenderState:n$})},nJ=t=>e=>e.test(t),n0=[H,to,tr,tn,tl,ta,{test:t=>"auto"===t,parse:t=>t}],n1=t=>n0.find(nJ(t)),n2=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),n5=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,n3=t=>/^0[^.\s]+$/u.test(t),n9=new Set(["brightness","contrast","saturate","opacity"]);function n4(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(_)||[];if(!s)return t;let n=i.replace(s,""),r=+!!n9.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let n6=/\b([a-z-]*)\(.*?\)/gu,n8={...tT,getAnimatableNone:t=>{let e=t.match(n6);return e?e.map(n4).join(" "):t}},n7={...nk,color:td,backgroundColor:td,outlineColor:td,fill:td,stroke:td,borderColor:td,borderTopColor:td,borderRightColor:td,borderBottomColor:td,borderLeftColor:td,filter:n8,WebkitFilter:n8},rt=t=>n7[t];function re(t,e){let i=rt(t);return i!==n8&&(i=tT),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let ri=new Set(["auto","none","0"]);class rs extends eV{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&Y(s=s.trim())){let n=function t(e,i,s=1){$(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=n5.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return n2(t)?parseFloat(t):t}return Y(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!T.has(i)||2!==t.length)return;let[s,n]=t,r=n1(s),o=n1(n);if(r!==o)if(ev(r)&&ev(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else ew[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||n3(s)))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!ri.has(e)&&tg(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=re(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ew[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=ew[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let rn=[...n0,td,tT],rr={current:null},ro={current:!1},ra=new WeakMap,rl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rh{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eV,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nx(e),this.isVariantNode=nT(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==a[t]&&D(e)&&e.set(a[t],!1)}}mount(t){this.current=t,ra.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ro.current||function(){if(ro.current=!0,nP.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rr.current=t.matches;t.addListener(e),e()}else rr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=x.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nS){let e=nS[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iT()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rl.length;e++){let i=rl[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(D(n))t.addValue(s,n);else if(D(r))t.addValue(s,V(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,V(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=V(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=i){if("string"==typeof i&&(n2(i)||n3(i)))i=parseFloat(i);else{let s;s=i,!rn.find(nJ(s))&&tT.test(e)&&(i=re(t,e))}this.setBaseTarget(t,D(i)?i.get():i)}return D(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=o(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||D(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new b),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class ru extends rh{constructor(){super(...arguments),this.KeyframeResolver=rs}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function rd(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}class rc extends ru{constructor(){super(...arguments),this.type="html",this.renderInstance=rd}readValueFromInstance(t,e){if(x.has(e))return this.projection?.isProjecting?ef(e):((t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eg(i,e)})(t,e);{let i=window.getComputedStyle(t),s=(W(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iR(t,e)}build(t,e,i){nL(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return nG(t,e,i)}}let rp=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class rm extends ru{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iT}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=rt(e);return t&&t.default||0}return e=rp.has(e)?e:R(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return nZ(t,e,i)}build(t,e,i){nU(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in rd(t,e,void 0,s),e.attrs)t.setAttribute(rp.has(i)?i:R(i),e.attrs[i])}mount(t){this.isSVGTag=nN(t.tagName),super.mount(t)}}let rf=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((e$={animation:{Feature:it},exit:{Feature:ii},inView:{Feature:nf},tap:{Feature:nh},focus:{Feature:ne},hover:{Feature:nt},pan:{Feature:i_},drag:{Feature:iq,ProjectionNode:s9,MeasureLayout:i8},layout:{ProjectionNode:s9,MeasureLayout:i8}},eN=(t,e)=>nH(t)?new rm(e):new rc(e,{allowProjection:t!==iJ.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:n}){function r(t,r){var o;let a,l={...(0,iJ.useContext)(ny.Q),...t,layoutId:function({layoutId:t}){let e=(0,iJ.useContext)(i1.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:h}=l,u=function(t){let{initial:e,animate:i}=function(t,e){if(nx(t)){let{initial:e,animate:i}=t;return{initial:!1===e||e1(e)?e:void 0,animate:e1(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,iJ.useContext)(nv));return(0,iJ.useMemo)(()=>({initial:e,animate:i}),[nw(e),nw(i)])}(t),d=s(t,h);if(!h&&nP.B){(0,iJ.useContext)(ng).strict;let t=function(t){let{drag:e,layout:i}=nS;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(l);a=t.MeasureLayout,u.visualElement=function(t,e,i,s,n){let{visualElement:r}=(0,iJ.useContext)(nv),o=(0,iJ.useContext)(ng),a=(0,iJ.useContext)(nM.t),l=(0,iJ.useContext)(ny.Q).reducedMotion,h=(0,iJ.useRef)(null);s=s||o.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:r,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let u=h.current,d=(0,iJ.useContext)(i2);u&&!u.projection&&n&&("html"===u.type||"svg"===u.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&iL(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,n,d);let c=(0,iJ.useRef)(!1);(0,iJ.useInsertionEffect)(()=>{u&&c.current&&u.update(i,a)});let p=i[j],m=(0,iJ.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,nE.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),iQ.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,iJ.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(n,d,l,e,t.ProjectionNode)}return(0,iZ.jsxs)(nv.Provider,{value:u,children:[a&&u.visualElement?(0,iZ.jsx)(a,{visualElement:u.visualElement,...l}):null,i(n,t,(o=u.visualElement,(0,iJ.useCallback)(t=>{t&&d.onMount&&d.onMount(t),o&&(t?o.mount(t):o.unmount()),r&&("function"==typeof r?r(t):iL(r)&&(r.current=t))},[o])),d,h,u.visualElement)]})}t&&function(t){for(let e in t)nS[e]={...nS[e],...t[e]}}(t),r.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let o=(0,iJ.forwardRef)(r);return o[nA]=n,o}({...nH(t)?nQ:n_,preloadedFeatures:e$,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(nH(e)?function(t,e,i,s){let n=(0,iJ.useMemo)(()=>{let i=n$();return nU(i,e,nN(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nB(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return nB(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,iJ.useMemo)(()=>{let i=nF();return nL(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(nY(n)||!0===i&&nz(n)||!e&&!nz(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==iJ.Fragment?{...a,...o,ref:s}:{},{children:h}=i,u=(0,iJ.useMemo)(()=>D(h)?h.get():h,[h]);return(0,iJ.createElement)(e,{...l,children:u})}}(e),createVisualElement:eN,Component:t})}))},9751:(t,e,i)=>{"use strict";i.d(e,{Q:()=>s});let s=(0,i(4232).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})}}]);