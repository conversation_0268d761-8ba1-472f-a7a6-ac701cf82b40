(()=>{var a={};a.id=636,a.ids=[636],a.modules={2015:a=>{"use strict";a.exports=require("react")},6539:(a,b,c)=>{"use strict";c.d(b,{bv:()=>e,i3:()=>d});let d={baseURL:"/api",uploadsURL:""},e=a=>a?(a.startsWith("http://")||a.startsWith("https://"),a):null},6745:(a,b,c)=>{"use strict";c.d(b,{$L:()=>w,Cm:()=>k,It:()=>j,KT:()=>i,OX:()=>p,WH:()=>m,aU:()=>h,bW:()=>q,dt:()=>u,ro:()=>s,sB:()=>o,t1:()=>v,ub:()=>r,wf:()=>t,y9:()=>l,yI:()=>n,zm:()=>g});let d=c(6539).i3.baseURL,e=async(a,b={})=>{try{let c=await fetch(`${d}${a}`,{headers:{"Content-Type":"application/json",...b.headers},...b});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(b){throw console.error(`API call failed for ${a}:`,b),b}},f=(a=500)=>new Promise(b=>setTimeout(b,a)),g=async()=>{try{return await f(),await e("/api/hero")}catch(a){throw console.error("خطأ في جلب بيانات البانر الرئيسي:",a),a}},h=async(a,b=null)=>{try{await f();let b=await e("/api/hero"),c=b?.id||1;return await e(`/api/hero/${c}`,{method:"PUT",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في تحديث بيانات البانر الرئيسي:",a),a}},i=async()=>{try{return await f(),await e("/api/kitchens")}catch(a){throw console.error("خطأ في جلب بيانات المطابخ:",a),a}},j=async(a,b=null)=>{try{return await f(),await e("/kitchens",{method:"POST",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في إضافة مطبخ:",a),a}},k=async(a,b,c=null)=>{try{return await f(),await e(`/api/kitchens/${a}`,{method:"PUT",body:JSON.stringify(b)})}catch(a){throw console.error("خطأ في تحديث مطبخ:",a),a}},l=async(a,b=null)=>{try{return await f(),await e(`/api/kitchens/${a}`,{method:"DELETE"})}catch(a){throw console.error("خطأ في حذف مطبخ:",a),a}},m=async()=>{try{return await f(),await e("/api/cabinets")}catch(a){throw console.error("خطأ في جلب بيانات الخزائن:",a),a}},n=async(a,b=null)=>{try{return await f(),await e("/cabinets",{method:"POST",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في إضافة خزانة:",a),a}},o=async(a,b,c=null)=>{try{return await f(),await e(`/api/cabinets/${a}`,{method:"PUT",body:JSON.stringify(b)})}catch(a){throw console.error("خطأ في تحديث خزانة:",a),a}},p=async(a,b=null)=>{try{return await f(),await e(`/api/cabinets/${a}`,{method:"DELETE"})}catch(a){throw console.error("خطأ في حذف خزانة:",a),a}},q=async()=>{try{return await f(),await e("/api/categories")}catch(a){throw console.error("خطأ في جلب الفئات:",a),a}},r=async()=>{try{return await f(),await e("/api/why-choose-us")}catch(a){throw console.error("خطأ في جلب بيانات لماذا تختارنا:",a),a}},s=async(a,b=null)=>{try{return await f(),await e("/why-choose-us",{method:"PUT",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في تحديث بيانات لماذا تختارنا:",a),a}},t=async()=>{try{return await f(),await e("/api/footer")}catch(a){throw console.error("خطأ في جلب بيانات التذييل:",a),a}},u=async(a,b=null)=>{try{return await f(),await e("/footer",{method:"PUT",body:JSON.stringify(a)})}catch(a){throw console.error("خطأ في تحديث بيانات التذييل:",a),a}},v=async()=>{try{return await f(),await e("/api/company-settings")}catch(a){throw console.error("خطأ في جلب إعدادات الشركة:",a),a}},w=async(a,b,c=null)=>{try{return await f(),{success:!0}}catch(a){throw console.error("خطأ في تحديث إعدادات الشركة:",a),a}}},7522:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(8732);function e({Component:a,pageProps:b}){return(0,d.jsx)("div",{dir:"rtl",children:(0,d.jsx)(a,{...b})})}c(8754),c(2015),c(8015)},8015:(a,b,c)=>{"use strict";c.d(b,{aB:()=>f,wU:()=>e});var d=c(6745);let e=async()=>{try{return console.log("\uD83D\uDD04 Initializing database connection..."),await Promise.all([(0,d.zm)(),(0,d.ub)(),(0,d.KT)(),(0,d.WH)(),(0,d.wf)(),(0,d.bW)(),(0,d.t1)()]),console.log("✅ Database connection initialized successfully"),!0}catch(a){return console.error("❌ Failed to initialize database connection:",a),console.error("تأكد من أن API server يعمل على http://localhost:3002"),!1}},f=async()=>{try{return console.log("\uD83D\uDD04 Force resetting database..."),await e(),console.log("✅ Database connection reset completed"),!0}catch(a){return console.error("❌ Failed to force reset database:",a),!1}}},8732:a=>{"use strict";a.exports=require("react/jsx-runtime")},8754:()=>{}};var b=require("../webpack-runtime.js");b.C(a);var c=b(b.s=7522);module.exports=c})();