/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "(pages-dir-node)/./database/api-client.js":
/*!********************************!*\
  !*** ./database/api-client.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addCabinet: () => (/* binding */ addCabinet),\n/* harmony export */   addKitchen: () => (/* binding */ addKitchen),\n/* harmony export */   deleteCabinet: () => (/* binding */ deleteCabinet),\n/* harmony export */   deleteKitchen: () => (/* binding */ deleteKitchen),\n/* harmony export */   getCabinetsData: () => (/* binding */ getCabinetsData),\n/* harmony export */   getCategories: () => (/* binding */ getCategories),\n/* harmony export */   getCompanySettings: () => (/* binding */ getCompanySettings),\n/* harmony export */   getFooterData: () => (/* binding */ getFooterData),\n/* harmony export */   getHeroData: () => (/* binding */ getHeroData),\n/* harmony export */   getKitchensData: () => (/* binding */ getKitchensData),\n/* harmony export */   getWhyChooseUsData: () => (/* binding */ getWhyChooseUsData),\n/* harmony export */   logActivity: () => (/* binding */ logActivity),\n/* harmony export */   updateCabinet: () => (/* binding */ updateCabinet),\n/* harmony export */   updateCompanySetting: () => (/* binding */ updateCompanySetting),\n/* harmony export */   updateFooterData: () => (/* binding */ updateFooterData),\n/* harmony export */   updateHeroData: () => (/* binding */ updateHeroData),\n/* harmony export */   updateKitchen: () => (/* binding */ updateKitchen),\n/* harmony export */   updateWhyChooseUsData: () => (/* binding */ updateWhyChooseUsData)\n/* harmony export */ });\n/* harmony import */ var _src_config_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../src/config/api.js */ \"(pages-dir-node)/./src/config/api.js\");\n// API Client for Khobra Kitchens - Frontend to Backend Communication\n// عميل API لخبرة المطابخ - التواصل بين الواجهة الأمامية والخلفية\n\n// Determine API base URL based on environment\nconst API_BASE_URL = _src_config_api_js__WEBPACK_IMPORTED_MODULE_0__.API_CONFIG.baseURL;\n// Helper function for API calls\nconst apiCall = async (endpoint, options = {})=>{\n    try {\n        const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(`API call failed for ${endpoint}:`, error);\n        throw error;\n    }\n};\n// Delay function for simulating loading\nconst delay = (ms = 500)=>new Promise((resolve)=>setTimeout(resolve, ms));\n// ==================== Hero Section API ====================\nconst getHeroData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/hero');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات البانر الرئيسي:', error);\n        throw error;\n    }\n};\nconst updateHeroData = async (data, userId = null)=>{\n    try {\n        await delay();\n        // First get the current hero data to get the ID\n        const currentHero = await apiCall('/hero');\n        const heroId = currentHero?.id || 1; // Default to 1 if no hero exists\n        return await apiCall(`/hero/${heroId}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث بيانات البانر الرئيسي:', error);\n        throw error;\n    }\n};\n// ==================== Kitchens API ====================\nconst getKitchensData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/kitchens');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات المطابخ:', error);\n        throw error;\n    }\n};\nconst addKitchen = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/kitchens', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة مطبخ:', error);\n        throw error;\n    }\n};\nconst updateKitchen = async (id, data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/kitchens/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث مطبخ:', error);\n        throw error;\n    }\n};\nconst deleteKitchen = async (id, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/kitchens/${id}`, {\n            method: 'DELETE'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف مطبخ:', error);\n        throw error;\n    }\n};\n// ==================== Cabinets API ====================\nconst getCabinetsData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/cabinets');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات الخزائن:', error);\n        throw error;\n    }\n};\nconst addCabinet = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/cabinets', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة خزانة:', error);\n        throw error;\n    }\n};\nconst updateCabinet = async (id, data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/cabinets/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث خزانة:', error);\n        throw error;\n    }\n};\nconst deleteCabinet = async (id, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/cabinets/${id}`, {\n            method: 'DELETE'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف خزانة:', error);\n        throw error;\n    }\n};\n// ==================== Categories API ====================\nconst getCategories = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/categories');\n    } catch (error) {\n        console.error('خطأ في جلب الفئات:', error);\n        throw error;\n    }\n};\n// ==================== Why Choose Us API ====================\nconst getWhyChooseUsData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/why-choose-us');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات لماذا تختارنا:', error);\n        throw error;\n    }\n};\nconst updateWhyChooseUsData = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/why-choose-us', {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث بيانات لماذا تختارنا:', error);\n        throw error;\n    }\n};\n// ==================== Footer API ====================\nconst getFooterData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/footer');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات التذييل:', error);\n        throw error;\n    }\n};\nconst updateFooterData = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/footer', {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث بيانات التذييل:', error);\n        throw error;\n    }\n};\n// ==================== Company Settings API ====================\nconst getCompanySettings = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/company-settings');\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات الشركة:', error);\n        throw error;\n    }\n};\nconst updateCompanySetting = async (key, value, userId = null)=>{\n    try {\n        await delay();\n        // For now, just return success since this section doesn't have update endpoint yet\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('خطأ في تحديث إعدادات الشركة:', error);\n        throw error;\n    }\n};\n// ==================== Activity Log API ====================\nconst logActivity = async (userId, action, tableName, recordId, oldValues, newValues)=>{\n    try {\n        // For now, just log to console\n        console.log('Activity logged:', {\n            userId,\n            action,\n            tableName,\n            recordId,\n            oldValues,\n            newValues\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('خطأ في تسجيل النشاط:', error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./database/api-client.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_index_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/index.css */ \"(pages-dir-node)/./src/index.css\");\n/* harmony import */ var _src_index_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_index_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _src_admin_utils_initDatabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../src/admin/utils/initDatabase */ \"(pages-dir-node)/./src/admin/utils/initDatabase.js\");\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"App.useEffect\": ()=>{\n            // Set client-side flag\n            setIsClient(true);\n            // Initialize database on app start (client-side only)\n            if (false) {}\n        }\n    }[\"App.useEffect\"], []);\n    // Don't render anything until we're on the client side\n    if (!isClient) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"/var/www/html/pages/_app.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/var/www/html/pages/_app.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXlCO0FBQ2tCO0FBQ3lCO0FBRXJELFNBQVNHLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdOLCtDQUFRQSxDQUFDO0lBRXpDRCxnREFBU0E7eUJBQUM7WUFDUix1QkFBdUI7WUFDdkJPLFlBQVk7WUFFWixzREFBc0Q7WUFDdEQsSUFBSSxLQUE2QixFQUFFLEVBRWxDO1FBQ0g7d0JBQUcsRUFBRTtJQUVMLHVEQUF1RDtJQUN2RCxJQUFJLENBQUNELFVBQVU7UUFDYixPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ0s7UUFBSUMsS0FBSTtrQkFDUCw0RUFBQ1I7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsiL3Zhci93d3cvaHRtbC9wYWdlcy9fYXBwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi4vc3JjL2luZGV4LmNzcydcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IGluaXRpYWxpemVEYXRhYmFzZSB9IGZyb20gJy4uL3NyYy9hZG1pbi91dGlscy9pbml0RGF0YWJhc2UnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH0pIHtcbiAgY29uc3QgW2lzQ2xpZW50LCBzZXRJc0NsaWVudF0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFNldCBjbGllbnQtc2lkZSBmbGFnXG4gICAgc2V0SXNDbGllbnQodHJ1ZSlcblxuICAgIC8vIEluaXRpYWxpemUgZGF0YWJhc2Ugb24gYXBwIHN0YXJ0IChjbGllbnQtc2lkZSBvbmx5KVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgaW5pdGlhbGl6ZURhdGFiYXNlKCkuY2F0Y2goY29uc29sZS5lcnJvcilcbiAgICB9XG4gIH0sIFtdKVxuXG4gIC8vIERvbid0IHJlbmRlciBhbnl0aGluZyB1bnRpbCB3ZSdyZSBvbiB0aGUgY2xpZW50IHNpZGVcbiAgaWYgKCFpc0NsaWVudCkge1xuICAgIHJldHVybiBudWxsXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgZGlyPVwicnRsXCI+XG4gICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImluaXRpYWxpemVEYXRhYmFzZSIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsImlzQ2xpZW50Iiwic2V0SXNDbGllbnQiLCJjYXRjaCIsImNvbnNvbGUiLCJlcnJvciIsImRpdiIsImRpciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/admin/utils/initDatabase.js":
/*!*****************************************!*\
  !*** ./src/admin/utils/initDatabase.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   forceResetDatabase: () => (/* binding */ forceResetDatabase),\n/* harmony export */   getDatabaseStatus: () => (/* binding */ getDatabaseStatus),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   isDatabaseInitialized: () => (/* binding */ isDatabaseInitialized),\n/* harmony export */   resetDatabase: () => (/* binding */ resetDatabase)\n/* harmony export */ });\n/* harmony import */ var _database_api_client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../database/api-client.js */ \"(pages-dir-node)/./database/api-client.js\");\n// Database initialization utility\n// أداة تهيئة قاعدة البيانات\n// This file ensures that the database is properly initialized\n// when the admin panel starts up\n// لا نحتاج إلى initDatabase من database.js لأننا نستخدم API backend الآن\n\n// Initialize all database collections with default data\nconst initializeDatabase = async ()=>{\n    try {\n        console.log('🔄 Initializing database connection...');\n        // Test API connection by calling get functions\n        // This will verify that the API backend is running\n        await Promise.all([\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getHeroData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getWhyChooseUsData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getKitchensData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getCabinetsData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getFooterData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getCategories)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getCompanySettings)()\n        ]);\n        console.log('✅ Database connection initialized successfully');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to initialize database connection:', error);\n        console.error('تأكد من أن API server يعمل على http://localhost:3002');\n        return false;\n    }\n};\n// Check if database has been initialized\nconst isDatabaseInitialized = ()=>{\n    try {\n        // Check if basic data exists in localStorage\n        const heroData = localStorage.getItem('db_hero_section');\n        const categoriesData = localStorage.getItem('db_categories');\n        return !!(heroData && categoriesData);\n    } catch (error) {\n        console.error('Error checking database initialization:', error);\n        return false;\n    }\n};\n// Reset database to default state\nconst resetDatabase = ()=>{\n    try {\n        console.log('🔄 Resetting database...');\n        // Clear all database storage\n        const dbKeys = [\n            'db_hero_section',\n            'db_why_choose_us',\n            'db_kitchens',\n            'db_cabinets',\n            'db_footer',\n            'db_categories',\n            'db_company_settings',\n            'db_activity_log'\n        ];\n        dbKeys.forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n        console.log('✅ Database reset successfully');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to reset database:', error);\n        return false;\n    }\n};\n// Export database status\nconst getDatabaseStatus = ()=>{\n    try {\n        const status = {\n            initialized: isDatabaseInitialized(),\n            collections: {}\n        };\n        // Check each collection\n        const collections = [\n            'db_hero_section',\n            'db_why_choose_us',\n            'db_kitchens',\n            'db_cabinets',\n            'db_footer',\n            'db_categories',\n            'db_company_settings'\n        ];\n        collections.forEach((collection)=>{\n            const data = localStorage.getItem(collection);\n            status.collections[collection] = {\n                exists: !!data,\n                size: data ? data.length : 0\n            };\n        });\n        return status;\n    } catch (error) {\n        console.error('Error getting database status:', error);\n        return {\n            initialized: false,\n            collections: {},\n            error: error.message\n        };\n    }\n};\n// Force reset database and recreate with new data\nconst forceResetDatabase = async ()=>{\n    try {\n        console.log('🔄 Force resetting database...');\n        // For SQLite backend, we would need to call a reset API endpoint\n        // For now, just reinitialize the connection\n        await initializeDatabase();\n        console.log('✅ Database connection reset completed');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to force reset database:', error);\n        return false;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    initializeDatabase,\n    isDatabaseInitialized,\n    resetDatabase,\n    getDatabaseStatus,\n    forceResetDatabase\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/admin/utils/initDatabase.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/config/api.js":
/*!***************************!*\
  !*** ./src/config/api.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   getApiURL: () => (/* binding */ getApiURL),\n/* harmony export */   getImageURL: () => (/* binding */ getImageURL)\n/* harmony export */ });\n// API Configuration\n// إعدادات API للبيئات المختلفة\nconst getApiConfig = ()=>{\n    // Check if we're in browser environment\n    if (true) {\n        // Server-side rendering - return default config\n        return {\n            baseURL: '/api',\n            uploadsURL: ''\n        };\n    }\n    // تحديد البيئة الحالية\n    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';\n    if (isDevelopment) {\n        return {\n            baseURL: 'http://localhost:3002',\n            uploadsURL: '' // روابط نسبية - الصور في public/uploads\n        };\n    } else {\n        // للإنتاج وجميع البيئات الأخرى\n        return {\n            baseURL: `${window.location.protocol}//${window.location.host}/api`,\n            uploadsURL: '' // روابط نسبية - الصور في public/uploads\n        };\n    }\n};\nconst API_CONFIG = getApiConfig();\n// دالة مساعدة لبناء URL كامل للصور\nconst getImageURL = (imagePath)=>{\n    if (!imagePath) return null;\n    // إذا كان الرابط كاملاً بالفعل، أرجعه كما هو\n    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n        return imagePath;\n    }\n    // الصور الآن في public/uploads، لذا نستخدم روابط نسبية مباشرة\n    // هذا يعمل مع HTTP و HTTPS تلقائياً\n    return imagePath;\n};\n// دالة مساعدة لبناء URL للـ API\nconst getApiURL = (endpoint)=>{\n    return `${API_CONFIG.baseURL}${endpoint}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/config/api.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/index.css":
/*!***********************!*\
  !*** ./src/index.css ***!
  \***********************/
/***/ (() => {



/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(pages-dir-node)/./pages/_app.js"));
module.exports = __webpack_exports__;

})();