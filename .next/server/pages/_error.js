/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "(pages-dir-node)/./database/api-client.js":
/*!********************************!*\
  !*** ./database/api-client.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addCabinet: () => (/* binding */ addCabinet),\n/* harmony export */   addKitchen: () => (/* binding */ addKitchen),\n/* harmony export */   deleteCabinet: () => (/* binding */ deleteCabinet),\n/* harmony export */   deleteKitchen: () => (/* binding */ deleteKitchen),\n/* harmony export */   getCabinetsData: () => (/* binding */ getCabinetsData),\n/* harmony export */   getCategories: () => (/* binding */ getCategories),\n/* harmony export */   getCompanySettings: () => (/* binding */ getCompanySettings),\n/* harmony export */   getFooterData: () => (/* binding */ getFooterData),\n/* harmony export */   getHeroData: () => (/* binding */ getHeroData),\n/* harmony export */   getKitchensData: () => (/* binding */ getKitchensData),\n/* harmony export */   getWhyChooseUsData: () => (/* binding */ getWhyChooseUsData),\n/* harmony export */   logActivity: () => (/* binding */ logActivity),\n/* harmony export */   updateCabinet: () => (/* binding */ updateCabinet),\n/* harmony export */   updateCompanySetting: () => (/* binding */ updateCompanySetting),\n/* harmony export */   updateFooterData: () => (/* binding */ updateFooterData),\n/* harmony export */   updateHeroData: () => (/* binding */ updateHeroData),\n/* harmony export */   updateKitchen: () => (/* binding */ updateKitchen),\n/* harmony export */   updateWhyChooseUsData: () => (/* binding */ updateWhyChooseUsData)\n/* harmony export */ });\n/* harmony import */ var _src_config_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../src/config/api.js */ \"(pages-dir-node)/./src/config/api.js\");\n// API Client for Khobra Kitchens - Frontend to Backend Communication\n// عميل API لخبرة المطابخ - التواصل بين الواجهة الأمامية والخلفية\n\n// Determine API base URL based on environment\nconst API_BASE_URL = _src_config_api_js__WEBPACK_IMPORTED_MODULE_0__.API_CONFIG.baseURL;\n// Helper function for API calls\nconst apiCall = async (endpoint, options = {})=>{\n    try {\n        const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(`API call failed for ${endpoint}:`, error);\n        throw error;\n    }\n};\n// Delay function for simulating loading\nconst delay = (ms = 500)=>new Promise((resolve)=>setTimeout(resolve, ms));\n// ==================== Hero Section API ====================\nconst getHeroData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/hero');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات البانر الرئيسي:', error);\n        throw error;\n    }\n};\nconst updateHeroData = async (data, userId = null)=>{\n    try {\n        await delay();\n        // First get the current hero data to get the ID\n        const currentHero = await apiCall('/hero');\n        const heroId = currentHero?.id || 1; // Default to 1 if no hero exists\n        return await apiCall(`/hero/${heroId}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث بيانات البانر الرئيسي:', error);\n        throw error;\n    }\n};\n// ==================== Kitchens API ====================\nconst getKitchensData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/kitchens');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات المطابخ:', error);\n        throw error;\n    }\n};\nconst addKitchen = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/kitchens', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة مطبخ:', error);\n        throw error;\n    }\n};\nconst updateKitchen = async (id, data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/kitchens/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث مطبخ:', error);\n        throw error;\n    }\n};\nconst deleteKitchen = async (id, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/kitchens/${id}`, {\n            method: 'DELETE'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف مطبخ:', error);\n        throw error;\n    }\n};\n// ==================== Cabinets API ====================\nconst getCabinetsData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/cabinets');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات الخزائن:', error);\n        throw error;\n    }\n};\nconst addCabinet = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/cabinets', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة خزانة:', error);\n        throw error;\n    }\n};\nconst updateCabinet = async (id, data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/cabinets/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث خزانة:', error);\n        throw error;\n    }\n};\nconst deleteCabinet = async (id, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/cabinets/${id}`, {\n            method: 'DELETE'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف خزانة:', error);\n        throw error;\n    }\n};\n// ==================== Categories API ====================\nconst getCategories = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/categories');\n    } catch (error) {\n        console.error('خطأ في جلب الفئات:', error);\n        throw error;\n    }\n};\n// ==================== Why Choose Us API ====================\nconst getWhyChooseUsData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/why-choose-us');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات لماذا تختارنا:', error);\n        throw error;\n    }\n};\nconst updateWhyChooseUsData = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/why-choose-us', {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث بيانات لماذا تختارنا:', error);\n        throw error;\n    }\n};\n// ==================== Footer API ====================\nconst getFooterData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/footer');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات التذييل:', error);\n        throw error;\n    }\n};\nconst updateFooterData = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/footer', {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث بيانات التذييل:', error);\n        throw error;\n    }\n};\n// ==================== Company Settings API ====================\nconst getCompanySettings = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/company-settings');\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات الشركة:', error);\n        throw error;\n    }\n};\nconst updateCompanySetting = async (key, value, userId = null)=>{\n    try {\n        await delay();\n        // For now, just return success since this section doesn't have update endpoint yet\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('خطأ في تحديث إعدادات الشركة:', error);\n        throw error;\n    }\n};\n// ==================== Activity Log API ====================\nconst logActivity = async (userId, action, tableName, recordId, oldValues, newValues)=>{\n    try {\n        // For now, just log to console\n        console.log('Activity logged:', {\n            userId,\n            action,\n            tableName,\n            recordId,\n            oldValues,\n            newValues\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('خطأ في تسجيل النشاط:', error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./database/api-client.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/format-url */ \"next/dist/shared/lib/router/utils/format-url\");\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(pages-dir-node)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(pages-dir-node)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/shared/lib/page-path/normalize-data-path */ \"next/dist/shared/lib/page-path/normalize-data-path\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! private-next-pages/_error */ \"(pages-dir-node)/./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/shared/lib/utils */ \"next/dist/shared/lib/utils\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/lib/redirect-status */ \"(pages-dir-node)/./node_modules/next/dist/lib/redirect-status.js\");\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/lib/constants */ \"(pages-dir-node)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(pages-dir-node)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(pages-dir-node)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/server/response-cache/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/utils.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(pages-dir-node)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/add-path-prefix */ \"next/dist/shared/lib/router/utils/add-path-prefix\");\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/remove-trailing-slash */ \"next/dist/shared/lib/router/utils/remove-trailing-slash\");\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__);\n\n\n\n\n\n\n\n\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__\n});\nasync function handler(req, res, ctx) {\n    var _serverFilesManifest_config_experimental, _serverFilesManifest_config;\n    let srcPage = \"/_error\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return;\n    }\n    const { buildId, query, params, parsedUrl, originalQuery, originalPathname, buildManifest, nextFontManifest, serverFilesManifest, reactLoadableManifest, prerenderManifest, isDraftMode, isOnDemandRevalidate, revalidateOnlyGenerated, locale, locales, defaultLocale, routerServerContext, nextConfig, resolvedPathname } = prepareResult;\n    const isExperimentalCompile = serverFilesManifest == null ? void 0 : (_serverFilesManifest_config = serverFilesManifest.config) == null ? void 0 : (_serverFilesManifest_config_experimental = _serverFilesManifest_config.experimental) == null ? void 0 : _serverFilesManifest_config_experimental.isExperimentalCompile;\n    const hasServerProps = Boolean(getServerSideProps);\n    const hasStaticProps = Boolean(getStaticProps);\n    const hasStaticPaths = Boolean(getStaticPaths);\n    const hasGetInitialProps = Boolean(((private_next_pages_error__WEBPACK_IMPORTED_MODULE_13___default()) || private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__).getInitialProps);\n    const isAmp = query.amp && config.amp;\n    let cacheKey = null;\n    let isIsrFallback = false;\n    let isNextDataRequest = prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps);\n    const is404Page = srcPage === '/404';\n    const is500Page = srcPage === '/500';\n    const isErrorPage = srcPage === '/_error';\n    if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n        cacheKey = `${locale ? `/${locale}` : ''}${(srcPage === '/' || resolvedPathname === '/') && locale ? '' : resolvedPathname}${isAmp ? '.amp' : ''}`;\n        if (is404Page || is500Page || isErrorPage) {\n            cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`;\n        }\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    if (hasStaticPaths && !isDraftMode) {\n        const decodedPathname = (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(locale ? (0,next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__.addPathPrefix)(resolvedPathname, `/${locale}`) : resolvedPathname);\n        const isPrerendered = Boolean(prerenderManifest.routes[decodedPathname]) || prerenderManifest.notFoundRoutes.includes(decodedPathname);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__.NoFallbackError();\n            }\n            if (typeof prerenderInfo.fallback === 'string' && !isPrerendered && !isNextDataRequest) {\n                isIsrFallback = true;\n            }\n        }\n    }\n    // When serving a bot request, we want to serve a blocking render and not\n    // the prerendered page. This ensures that the correct content is served\n    // to the bot in the head.\n    if (isIsrFallback && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__.isBot)(req.headers['user-agent'] || '') || (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n        isIsrFallback = false;\n    }\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const method = req.method || 'GET';\n        const resolvedUrl = (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n            pathname: nextConfig.trailingSlash ? parsedUrl.pathname : (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(parsedUrl.pathname || '/'),\n            // make sure to only add query values from original URL\n            query: hasStaticProps ? {} : originalQuery\n        });\n        const publicRuntimeConfig = (routerServerContext == null ? void 0 : routerServerContext.publicRuntimeConfig) || nextConfig.publicRuntimeConfig;\n        const handleResponse = async (span)=>{\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                var _previousCacheEntry_value;\n                const doRender = async ()=>{\n                    try {\n                        var _nextConfig_i18n, _nextConfig_experimental_amp, _nextConfig_experimental_amp1;\n                        return await routeModule.render(req, res, {\n                            query: hasStaticProps && !isExperimentalCompile ? {\n                                ...params,\n                                ...isAmp ? {\n                                    amp: query.amp\n                                } : {}\n                            } : {\n                                ...query,\n                                ...params\n                            },\n                            params,\n                            page: srcPage,\n                            renderContext: {\n                                isDraftMode,\n                                isFallback: isIsrFallback,\n                                developmentNotFoundSourcePage: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'developmentNotFoundSourcePage')\n                            },\n                            sharedContext: {\n                                buildId,\n                                customServer: Boolean(routerServerContext == null ? void 0 : routerServerContext.isCustomServer) || undefined,\n                                deploymentId: false\n                            },\n                            renderOpts: {\n                                params,\n                                routeModule,\n                                page: srcPage,\n                                pageConfig: config || {},\n                                Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__),\n                                ComponentMod: private_next_pages_error__WEBPACK_IMPORTED_MODULE_13__,\n                                getStaticProps,\n                                getStaticPaths,\n                                getServerSideProps,\n                                supportsDynamicResponse: !hasStaticProps,\n                                buildManifest,\n                                nextFontManifest,\n                                reactLoadableManifest,\n                                assetPrefix: nextConfig.assetPrefix,\n                                strictNextHead: Boolean(nextConfig.experimental.strictNextHead),\n                                previewProps: prerenderManifest.preview,\n                                images: nextConfig.images,\n                                nextConfigOutput: nextConfig.output,\n                                optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                                nextScriptWorkers: Boolean(nextConfig.experimental.nextScriptWorkers),\n                                domainLocales: (_nextConfig_i18n = nextConfig.i18n) == null ? void 0 : _nextConfig_i18n.domains,\n                                crossOrigin: nextConfig.crossOrigin,\n                                multiZoneDraftMode,\n                                basePath: nextConfig.basePath,\n                                canonicalBase: nextConfig.amp.canonicalBase || '',\n                                ampOptimizerConfig: (_nextConfig_experimental_amp = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp.optimizer,\n                                disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n                                largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n                                // Only the `publicRuntimeConfig` key is exposed to the client side\n                                // It'll be rendered as part of __NEXT_DATA__ on the client side\n                                runtimeConfig: Object.keys(publicRuntimeConfig).length > 0 ? publicRuntimeConfig : undefined,\n                                isExperimentalCompile,\n                                experimental: {\n                                    clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                                },\n                                locale,\n                                locales,\n                                defaultLocale,\n                                setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                                isNextDataRequest: isNextDataRequest && (hasServerProps || hasStaticProps),\n                                resolvedUrl,\n                                // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                                // and not the resolved URL to prevent a hydration mismatch on\n                                // asPath\n                                resolvedAsPath: hasServerProps || hasGetInitialProps ? (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n                                    // we use the original URL pathname less the _next/data prefix if\n                                    // present\n                                    pathname: isNextDataRequest ? (0,next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__.normalizeDataPath)(originalPathname) : originalPathname,\n                                    query: originalQuery\n                                }) : resolvedUrl,\n                                isOnDemandRevalidate,\n                                ErrorDebug: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'PagesErrorDebug'),\n                                err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'invokeError'),\n                                dev: routeModule.isDev,\n                                // needed for experimental.optimizeCss feature\n                                distDir: `${routeModule.projectDir}/${routeModule.distDir}`,\n                                ampSkipValidation: (_nextConfig_experimental_amp1 = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp1.skipValidation,\n                                ampValidator: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'ampValidator')\n                            }\n                        }).then((renderResult)=>{\n                            const { metadata } = renderResult;\n                            let cacheControl = metadata.cacheControl;\n                            if ('isNotFound' in metadata && metadata.isNotFound) {\n                                return {\n                                    value: null,\n                                    cacheControl\n                                };\n                            }\n                            // Handle `isRedirect`.\n                            if (metadata.isRedirect) {\n                                return {\n                                    value: {\n                                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT,\n                                        props: metadata.pageData ?? metadata.flightData\n                                    },\n                                    cacheControl\n                                };\n                            }\n                            return {\n                                value: {\n                                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                                    html: renderResult,\n                                    pageData: renderResult.metadata.pageData,\n                                    headers: renderResult.metadata.headers,\n                                    status: renderResult.metadata.statusCode\n                                },\n                                cacheControl\n                            };\n                        }).finally(()=>{\n                            if (!span) return;\n                            span.setAttributes({\n                                'http.status_code': res.statusCode,\n                                'next.rsc': false\n                            });\n                            const rootSpanAttributes = tracer.getRootSpanAttributes();\n                            // We were unable to get attributes, probably OTEL is not enabled\n                            if (!rootSpanAttributes) {\n                                return;\n                            }\n                            if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest) {\n                                console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                                return;\n                            }\n                            const route = rootSpanAttributes.get('next.route');\n                            if (route) {\n                                const name = `${method} ${route}`;\n                                span.setAttributes({\n                                    'next.route': route,\n                                    'http.route': route,\n                                    'next.span_name': name\n                                });\n                                span.updateName(name);\n                            } else {\n                                span.updateName(`${method} ${req.url}`);\n                            }\n                        });\n                    } catch (err) {\n                        // if this is a background revalidate we need to report\n                        // the request error here as it won't be bubbled\n                        if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                            await routeModule.onRequestError(req, err, {\n                                routerKind: 'Pages Router',\n                                routePath: srcPage,\n                                routeType: 'render',\n                                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                                    isRevalidate: hasStaticProps,\n                                    isOnDemandRevalidate\n                                })\n                            }, routerServerContext);\n                        }\n                        throw err;\n                    }\n                };\n                // if we've already generated this page we no longer\n                // serve the fallback\n                if (previousCacheEntry) {\n                    isIsrFallback = false;\n                }\n                if (isIsrFallback) {\n                    const fallbackResponse = await routeModule.getResponseCache(req).get(routeModule.isDev ? null : locale ? `/${locale}${srcPage}` : srcPage, async ({ previousCacheEntry: previousFallbackCacheEntry = null })=>{\n                        if (!routeModule.isDev) {\n                            return (0,next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__.toResponseCacheEntry)(previousFallbackCacheEntry);\n                        }\n                        return doRender();\n                    }, {\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                        isFallback: true,\n                        isRoutePPREnabled: false,\n                        isOnDemandRevalidate: false,\n                        incrementalCache: await routeModule.getIncrementalCache(req, nextConfig, prerenderManifest),\n                        waitUntil: ctx.waitUntil\n                    });\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        fallbackResponse.isMiss = true;\n                        return fallbackResponse;\n                    }\n                }\n                if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                    res.statusCode = 404;\n                    // on-demand revalidate always sets this header\n                    res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                    res.end('This page could not be found');\n                    return null;\n                }\n                if (isIsrFallback && (previousCacheEntry == null ? void 0 : (_previousCacheEntry_value = previousCacheEntry.value) == null ? void 0 : _previousCacheEntry_value.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                    return {\n                        value: {\n                            kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                            html: new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(previousCacheEntry.value.html), {\n                                contentType: 'text/html;utf-8',\n                                metadata: {\n                                    statusCode: previousCacheEntry.value.status,\n                                    headers: previousCacheEntry.value.headers\n                                }\n                            }),\n                            pageData: {},\n                            status: previousCacheEntry.value.status,\n                            headers: previousCacheEntry.value.headers\n                        },\n                        cacheControl: {\n                            revalidate: 0,\n                            expire: undefined\n                        }\n                    };\n                }\n                return doRender();\n            };\n            const result = await routeModule.handleResponse({\n                cacheKey,\n                req,\n                nextConfig,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                waitUntil: ctx.waitUntil,\n                responseGenerator: responseGenerator,\n                prerenderManifest\n            });\n            // if we got a cache hit this wasn't an ISR fallback\n            // but it wasn't generated during build so isn't in the\n            // prerender-manifest\n            if (isIsrFallback && !(result == null ? void 0 : result.isMiss)) {\n                isIsrFallback = false;\n            }\n            // response is finished is no cache entry\n            if (!result) {\n                return;\n            }\n            if (hasStaticProps && !(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : result.isMiss ? 'MISS' : result.isStale ? 'STALE' : 'HIT');\n            }\n            let cacheControl;\n            if (!hasStaticProps || isIsrFallback) {\n                if (!res.getHeader('Cache-Control')) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                }\n            } else if (is404Page) {\n                const notFoundRevalidate = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'notFoundRevalidate');\n                cacheControl = {\n                    revalidate: typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n                    expire: undefined\n                };\n            } else if (is500Page) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (result.cacheControl) {\n                // If the cache entry has a cache control with a revalidate value that's\n                // a number, use it.\n                if (typeof result.cacheControl.revalidate === 'number') {\n                    var _result_cacheControl;\n                    if (result.cacheControl.revalidate < 1) {\n                        throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                            value: \"E22\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    cacheControl = {\n                        revalidate: result.cacheControl.revalidate,\n                        expire: ((_result_cacheControl = result.cacheControl) == null ? void 0 : _result_cacheControl.expire) ?? nextConfig.expireTime\n                    };\n                } else {\n                    // revalidate: false\n                    cacheControl = {\n                        revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__.CACHE_ONE_YEAR,\n                        expire: undefined\n                    };\n                }\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheControl && !res.getHeader('Cache-Control')) {\n                res.setHeader('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__.getCacheControlHeader)(cacheControl));\n            }\n            // notFound: true case\n            if (!result.value) {\n                var _result_cacheControl1;\n                // add revalidate metadata before rendering 404 page\n                // so that we can use this as source of truth for the\n                // cache-control header instead of what the 404 page returns\n                // for the revalidate value\n                (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.addRequestMeta)(req, 'notFoundRevalidate', (_result_cacheControl1 = result.cacheControl) == null ? void 0 : _result_cacheControl1.revalidate);\n                res.statusCode = 404;\n                if (isNextDataRequest) {\n                    res.end('{\"notFound\":true}');\n                    return;\n                }\n                // TODO: should route-module itself handle rendering the 404\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res, parsedUrl, false);\n                } else {\n                    res.end('This page could not be found');\n                }\n                return;\n            }\n            if (result.value.kind === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT) {\n                if (isNextDataRequest) {\n                    res.setHeader('content-type', 'application/json');\n                    res.end(JSON.stringify(result.value.props));\n                    return;\n                } else {\n                    const handleRedirect = (pageData)=>{\n                        const redirect = {\n                            destination: pageData.pageProps.__N_REDIRECT,\n                            statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n                            basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH\n                        };\n                        const statusCode = (0,next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__.getRedirectStatus)(redirect);\n                        const { basePath } = nextConfig;\n                        if (basePath && redirect.basePath !== false && redirect.destination.startsWith('/')) {\n                            redirect.destination = `${basePath}${redirect.destination}`;\n                        }\n                        if (redirect.destination.startsWith('/')) {\n                            redirect.destination = (0,next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__.normalizeRepeatedSlashes)(redirect.destination);\n                        }\n                        res.statusCode = statusCode;\n                        res.setHeader('Location', redirect.destination);\n                        if (statusCode === next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__.RedirectStatusCode.PermanentRedirect) {\n                            res.setHeader('Refresh', `0;url=${redirect.destination}`);\n                        }\n                        res.end(redirect.destination);\n                    };\n                    await handleRedirect(result.value.props);\n                    return null;\n                }\n            }\n            if (result.value.kind !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                throw Object.defineProperty(new Error(`Invariant: received non-pages cache entry in pages handler`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E695\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // when invoking _error before pages/500 we don't actually\n            // send the _error response\n            if ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'customErrorRender') || isErrorPage && (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && res.statusCode === 500) {\n                return null;\n            }\n            await (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__.sendRenderResult)({\n                req,\n                res,\n                // If we are rendering the error page it's not a data request\n                // anymore\n                result: isNextDataRequest && !isErrorPage && !is500Page ? new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(JSON.stringify(result.value.pageData)), {\n                    contentType: 'application/json',\n                    metadata: result.value.html.metadata\n                }) : result.value.html,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                cacheControl: routeModule.isDev ? undefined : cacheControl,\n                type: isNextDataRequest ? 'json' : 'html'\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse();\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        await routeModule.onRequestError(req, err, {\n            routerKind: 'Pages Router',\n            routePath: srcPage,\n            routeType: 'render',\n            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                isRevalidate: hasStaticProps,\n                isOnDemandRevalidate\n            })\n        }, routerServerContext);\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_index_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/index.css */ \"(pages-dir-node)/./src/index.css\");\n/* harmony import */ var _src_index_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_index_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _src_admin_utils_initDatabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../src/admin/utils/initDatabase */ \"(pages-dir-node)/./src/admin/utils/initDatabase.js\");\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"App.useEffect\": ()=>{\n            // Set client-side flag\n            setIsClient(true);\n            // Initialize database on app start (client-side only)\n            if (false) {}\n        }\n    }[\"App.useEffect\"], []);\n    // Don't render anything until we're on the client side\n    if (!isClient) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"/var/www/html/pages/_app.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/var/www/html/pages/_app.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXlCO0FBQ2tCO0FBQ3lCO0FBRXJELFNBQVNHLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdOLCtDQUFRQSxDQUFDO0lBRXpDRCxnREFBU0E7eUJBQUM7WUFDUix1QkFBdUI7WUFDdkJPLFlBQVk7WUFFWixzREFBc0Q7WUFDdEQsSUFBSSxLQUE2QixFQUFFLEVBRWxDO1FBQ0g7d0JBQUcsRUFBRTtJQUVMLHVEQUF1RDtJQUN2RCxJQUFJLENBQUNELFVBQVU7UUFDYixPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ0s7UUFBSUMsS0FBSTtrQkFDUCw0RUFBQ1I7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsiL3Zhci93d3cvaHRtbC9wYWdlcy9fYXBwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi4vc3JjL2luZGV4LmNzcydcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IGluaXRpYWxpemVEYXRhYmFzZSB9IGZyb20gJy4uL3NyYy9hZG1pbi91dGlscy9pbml0RGF0YWJhc2UnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH0pIHtcbiAgY29uc3QgW2lzQ2xpZW50LCBzZXRJc0NsaWVudF0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFNldCBjbGllbnQtc2lkZSBmbGFnXG4gICAgc2V0SXNDbGllbnQodHJ1ZSlcblxuICAgIC8vIEluaXRpYWxpemUgZGF0YWJhc2Ugb24gYXBwIHN0YXJ0IChjbGllbnQtc2lkZSBvbmx5KVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgaW5pdGlhbGl6ZURhdGFiYXNlKCkuY2F0Y2goY29uc29sZS5lcnJvcilcbiAgICB9XG4gIH0sIFtdKVxuXG4gIC8vIERvbid0IHJlbmRlciBhbnl0aGluZyB1bnRpbCB3ZSdyZSBvbiB0aGUgY2xpZW50IHNpZGVcbiAgaWYgKCFpc0NsaWVudCkge1xuICAgIHJldHVybiBudWxsXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgZGlyPVwicnRsXCI+XG4gICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImluaXRpYWxpemVEYXRhYmFzZSIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsImlzQ2xpZW50Iiwic2V0SXNDbGllbnQiLCJjYXRjaCIsImNvbnNvbGUiLCJlcnJvciIsImRpdiIsImRpciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        href: \"/icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.svg\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.svg\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"true\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"geo.region\",\n                        content: \"SA\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"geo.placename\",\n                        content: \"الرياض\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=yes\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://cdnjs.cloudflare.com\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/pages/_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/pages/_document.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/var/www/html/pages/_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/admin/utils/initDatabase.js":
/*!*****************************************!*\
  !*** ./src/admin/utils/initDatabase.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   forceResetDatabase: () => (/* binding */ forceResetDatabase),\n/* harmony export */   getDatabaseStatus: () => (/* binding */ getDatabaseStatus),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   isDatabaseInitialized: () => (/* binding */ isDatabaseInitialized),\n/* harmony export */   resetDatabase: () => (/* binding */ resetDatabase)\n/* harmony export */ });\n/* harmony import */ var _database_api_client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../database/api-client.js */ \"(pages-dir-node)/./database/api-client.js\");\n// Database initialization utility\n// أداة تهيئة قاعدة البيانات\n// This file ensures that the database is properly initialized\n// when the admin panel starts up\n// لا نحتاج إلى initDatabase من database.js لأننا نستخدم API backend الآن\n\n// Initialize all database collections with default data\nconst initializeDatabase = async ()=>{\n    try {\n        console.log('🔄 Initializing database connection...');\n        // Test API connection by calling get functions\n        // This will verify that the API backend is running\n        await Promise.all([\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getHeroData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getWhyChooseUsData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getKitchensData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getCabinetsData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getFooterData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getCategories)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getCompanySettings)()\n        ]);\n        console.log('✅ Database connection initialized successfully');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to initialize database connection:', error);\n        console.error('تأكد من أن API server يعمل على http://localhost:3002');\n        return false;\n    }\n};\n// Check if database has been initialized\nconst isDatabaseInitialized = ()=>{\n    try {\n        // Check if basic data exists in localStorage\n        const heroData = localStorage.getItem('db_hero_section');\n        const categoriesData = localStorage.getItem('db_categories');\n        return !!(heroData && categoriesData);\n    } catch (error) {\n        console.error('Error checking database initialization:', error);\n        return false;\n    }\n};\n// Reset database to default state\nconst resetDatabase = ()=>{\n    try {\n        console.log('🔄 Resetting database...');\n        // Clear all database storage\n        const dbKeys = [\n            'db_hero_section',\n            'db_why_choose_us',\n            'db_kitchens',\n            'db_cabinets',\n            'db_footer',\n            'db_categories',\n            'db_company_settings',\n            'db_activity_log'\n        ];\n        dbKeys.forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n        console.log('✅ Database reset successfully');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to reset database:', error);\n        return false;\n    }\n};\n// Export database status\nconst getDatabaseStatus = ()=>{\n    try {\n        const status = {\n            initialized: isDatabaseInitialized(),\n            collections: {}\n        };\n        // Check each collection\n        const collections = [\n            'db_hero_section',\n            'db_why_choose_us',\n            'db_kitchens',\n            'db_cabinets',\n            'db_footer',\n            'db_categories',\n            'db_company_settings'\n        ];\n        collections.forEach((collection)=>{\n            const data = localStorage.getItem(collection);\n            status.collections[collection] = {\n                exists: !!data,\n                size: data ? data.length : 0\n            };\n        });\n        return status;\n    } catch (error) {\n        console.error('Error getting database status:', error);\n        return {\n            initialized: false,\n            collections: {},\n            error: error.message\n        };\n    }\n};\n// Force reset database and recreate with new data\nconst forceResetDatabase = async ()=>{\n    try {\n        console.log('🔄 Force resetting database...');\n        // For SQLite backend, we would need to call a reset API endpoint\n        // For now, just reinitialize the connection\n        await initializeDatabase();\n        console.log('✅ Database connection reset completed');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to force reset database:', error);\n        return false;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    initializeDatabase,\n    isDatabaseInitialized,\n    resetDatabase,\n    getDatabaseStatus,\n    forceResetDatabase\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/admin/utils/initDatabase.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/config/api.js":
/*!***************************!*\
  !*** ./src/config/api.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   getApiURL: () => (/* binding */ getApiURL),\n/* harmony export */   getImageURL: () => (/* binding */ getImageURL)\n/* harmony export */ });\n// API Configuration\n// إعدادات API للبيئات المختلفة\nconst getApiConfig = ()=>{\n    // Check if we're in browser environment\n    if (true) {\n        // Server-side rendering - return default config\n        return {\n            baseURL: '/api',\n            uploadsURL: ''\n        };\n    }\n    // تحديد البيئة الحالية\n    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';\n    if (isDevelopment) {\n        return {\n            baseURL: 'http://localhost:3002',\n            uploadsURL: '' // روابط نسبية - الصور في public/uploads\n        };\n    } else {\n        // للإنتاج وجميع البيئات الأخرى\n        return {\n            baseURL: `${window.location.protocol}//${window.location.host}/api`,\n            uploadsURL: '' // روابط نسبية - الصور في public/uploads\n        };\n    }\n};\nconst API_CONFIG = getApiConfig();\n// دالة مساعدة لبناء URL كامل للصور\nconst getImageURL = (imagePath)=>{\n    if (!imagePath) return null;\n    // إذا كان الرابط كاملاً بالفعل، أرجعه كما هو\n    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n        return imagePath;\n    }\n    // الصور الآن في public/uploads، لذا نستخدم روابط نسبية مباشرة\n    // هذا يعمل مع HTTP و HTTPS تلقائياً\n    return imagePath;\n};\n// دالة مساعدة لبناء URL للـ API\nconst getApiURL = (endpoint)=>{\n    return `${API_CONFIG.baseURL}${endpoint}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/config/api.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/index.css":
/*!***********************!*\
  !*** ./src/index.css ***!
  \***********************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/page-path/normalize-data-path":
/*!*********************************************************************!*\
  !*** external "next/dist/shared/lib/page-path/normalize-data-path" ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/page-path/normalize-data-path");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/add-path-prefix":
/*!********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/add-path-prefix" ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/format-url":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/format-url" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-url");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/remove-trailing-slash":
/*!**************************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/remove-trailing-slash" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash");

/***/ }),

/***/ "next/dist/shared/lib/utils":
/*!*********************************************!*\
  !*** external "next/dist/shared/lib/utils" ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();