"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/[...slug]";
exports.ids = ["pages/api/[...slug]"];
exports.modules = {

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2F%5B...slug%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2F%5B...slug%5D.js&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2F%5B...slug%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2F%5B...slug%5D.js&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   handler: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/api-utils */ \"(api-node)/./node_modules/next/dist/server/api-utils/index.js\");\n/* harmony import */ var next_dist_server_api_utils__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_api_utils__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_slug_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pages/api/[...slug].js */ \"(api-node)/./pages/api/[...slug].js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(api-node)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(api-node)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n// Import the userland code.\n\n\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_3__.hoist)(_pages_api_slug_js__WEBPACK_IMPORTED_MODULE_4__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_3__.hoist)(_pages_api_slug_js__WEBPACK_IMPORTED_MODULE_4__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_2__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/[...slug]\",\n        pathname: \"/api/[...slug]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_slug_js__WEBPACK_IMPORTED_MODULE_4__,\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    let srcPage = \"/api/[...slug]\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {}\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return;\n    }\n    const { query, params, prerenderManifest } = prepareResult;\n    try {\n        const method = req.method || 'GET';\n        const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5__.getTracer)();\n        const activeSpan = tracer.getActiveScopeSpan();\n        const onRequestError = routeModule.instrumentationOnRequestError.bind(routeModule);\n        const invokeRouteModule = async (span)=>routeModule.render(req, res, {\n                query: {\n                    ...query,\n                    ...params\n                },\n                params,\n                allowedRevalidateHeaderKeys: undefined,\n                multiZoneDraftMode: Boolean(\"false\"),\n                trustHostHeader: undefined,\n                // TODO: get this from from runtime env so manifest\n                // doesn't need to load\n                previewProps: prerenderManifest.preview,\n                propagateError: false,\n                dev: routeModule.isDev,\n                page: \"/api/[...slug]\",\n                projectDir:  false || '',\n                onError: (...args)=>onRequestError(req, ...args)\n            }).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await invokeRouteModule(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, invokeRouteModule));\n        }\n    } catch (err) {\n        // we re-throw in dev to show the error overlay\n        if (routeModule.isDev) {\n            throw err;\n        }\n        // this is technically an invariant as error handling\n        // should be done inside of api-resolver onError\n        (0,next_dist_server_api_utils__WEBPACK_IMPORTED_MODULE_0__.sendError)(res, 500, 'Internal Server Error');\n    } finally{\n        // We don't allow any waitUntil work in pages API routes currently\n        // so if callback is present return with resolved promise since no\n        // pending work\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n    }\n}\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2F%5B...slug%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2F%5B...slug%5D.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/[...slug].js":
/*!********************************!*\
  !*** ./pages/api/[...slug].js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// API Proxy for Next.js\n// This file proxies all API requests to the external Express server\nasync function handler(req, res) {\n    const { slug } = req.query;\n    const path = Array.isArray(slug) ? slug.join('/') : slug;\n    // Build the external API URL\n    const apiUrl = `http://localhost:3002/api/${path}`;\n    console.log('API Proxy Request:', {\n        method: req.method,\n        path,\n        apiUrl,\n        slug\n    });\n    try {\n        // Forward the request to the external API\n        const response = await fetch(apiUrl, {\n            method: req.method,\n            headers: {\n                'Content-Type': 'application/json',\n                // Don't forward all headers to avoid conflicts\n                'User-Agent': 'Next.js API Proxy'\n            },\n            body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined\n        });\n        console.log('API Response Status:', response.status);\n        // Get the response data\n        const data = await response.json();\n        console.log('API Response Data:', data);\n        // Forward the response\n        res.status(response.status).json(data);\n    } catch (error) {\n        console.error('API Proxy Error:', error);\n        res.status(500).json({\n            error: 'Internal Server Error',\n            message: 'Failed to connect to API server',\n            details: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/[...slug].js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2F%5B...slug%5D&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2F%5B...slug%5D.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();