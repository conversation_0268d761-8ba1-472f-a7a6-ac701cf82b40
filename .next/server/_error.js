/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "/_error";
exports.ids = ["/_error"];
exports.modules = {

/***/ "./database/api-client.js":
/*!********************************!*\
  !*** ./database/api-client.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addCabinet: () => (/* binding */ addCabinet),\n/* harmony export */   addKitchen: () => (/* binding */ addKitchen),\n/* harmony export */   deleteCabinet: () => (/* binding */ deleteCabinet),\n/* harmony export */   deleteKitchen: () => (/* binding */ deleteKitchen),\n/* harmony export */   getCabinetsData: () => (/* binding */ getCabinetsData),\n/* harmony export */   getCategories: () => (/* binding */ getCategories),\n/* harmony export */   getCompanySettings: () => (/* binding */ getCompanySettings),\n/* harmony export */   getFooterData: () => (/* binding */ getFooterData),\n/* harmony export */   getHeroData: () => (/* binding */ getHeroData),\n/* harmony export */   getKitchensData: () => (/* binding */ getKitchensData),\n/* harmony export */   getWhyChooseUsData: () => (/* binding */ getWhyChooseUsData),\n/* harmony export */   logActivity: () => (/* binding */ logActivity),\n/* harmony export */   updateCabinet: () => (/* binding */ updateCabinet),\n/* harmony export */   updateCompanySetting: () => (/* binding */ updateCompanySetting),\n/* harmony export */   updateFooterData: () => (/* binding */ updateFooterData),\n/* harmony export */   updateHeroData: () => (/* binding */ updateHeroData),\n/* harmony export */   updateKitchen: () => (/* binding */ updateKitchen),\n/* harmony export */   updateWhyChooseUsData: () => (/* binding */ updateWhyChooseUsData)\n/* harmony export */ });\n/* harmony import */ var _src_config_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../src/config/api.js */ \"./src/config/api.js\");\n// API Client for Khobra Kitchens - Frontend to Backend Communication\n// عميل API لخبرة المطابخ - التواصل بين الواجهة الأمامية والخلفية\n\n// Determine API base URL based on environment\nconst API_BASE_URL = _src_config_api_js__WEBPACK_IMPORTED_MODULE_0__.API_CONFIG.baseURL;\n// Helper function for API calls\nconst apiCall = async (endpoint, options = {})=>{\n    try {\n        const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(`API call failed for ${endpoint}:`, error);\n        throw error;\n    }\n};\n// Delay function for simulating loading\nconst delay = (ms = 500)=>new Promise((resolve)=>setTimeout(resolve, ms));\n// ==================== Hero Section API ====================\nconst getHeroData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/api/hero');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات البانر الرئيسي:', error);\n        throw error;\n    }\n};\nconst updateHeroData = async (data, userId = null)=>{\n    try {\n        await delay();\n        // First get the current hero data to get the ID\n        const currentHero = await apiCall('/api/hero');\n        const heroId = currentHero?.id || 1; // Default to 1 if no hero exists\n        return await apiCall(`/api/hero/${heroId}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث بيانات البانر الرئيسي:', error);\n        throw error;\n    }\n};\n// ==================== Kitchens API ====================\nconst getKitchensData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/api/kitchens');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات المطابخ:', error);\n        throw error;\n    }\n};\nconst addKitchen = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/kitchens', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة مطبخ:', error);\n        throw error;\n    }\n};\nconst updateKitchen = async (id, data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/api/kitchens/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث مطبخ:', error);\n        throw error;\n    }\n};\nconst deleteKitchen = async (id, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/api/kitchens/${id}`, {\n            method: 'DELETE'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف مطبخ:', error);\n        throw error;\n    }\n};\n// ==================== Cabinets API ====================\nconst getCabinetsData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/api/cabinets');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات الخزائن:', error);\n        throw error;\n    }\n};\nconst addCabinet = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/cabinets', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في إضافة خزانة:', error);\n        throw error;\n    }\n};\nconst updateCabinet = async (id, data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/api/cabinets/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث خزانة:', error);\n        throw error;\n    }\n};\nconst deleteCabinet = async (id, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall(`/api/cabinets/${id}`, {\n            method: 'DELETE'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف خزانة:', error);\n        throw error;\n    }\n};\n// ==================== Categories API ====================\nconst getCategories = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/api/categories');\n    } catch (error) {\n        console.error('خطأ في جلب الفئات:', error);\n        throw error;\n    }\n};\n// ==================== Why Choose Us API ====================\nconst getWhyChooseUsData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/api/why-choose-us');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات لماذا تختارنا:', error);\n        throw error;\n    }\n};\nconst updateWhyChooseUsData = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/why-choose-us', {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث بيانات لماذا تختارنا:', error);\n        throw error;\n    }\n};\n// ==================== Footer API ====================\nconst getFooterData = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/api/footer');\n    } catch (error) {\n        console.error('خطأ في جلب بيانات التذييل:', error);\n        throw error;\n    }\n};\nconst updateFooterData = async (data, userId = null)=>{\n    try {\n        await delay();\n        return await apiCall('/footer', {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث بيانات التذييل:', error);\n        throw error;\n    }\n};\n// ==================== Company Settings API ====================\nconst getCompanySettings = async ()=>{\n    try {\n        await delay();\n        return await apiCall('/api/company-settings');\n    } catch (error) {\n        console.error('خطأ في جلب إعدادات الشركة:', error);\n        throw error;\n    }\n};\nconst updateCompanySetting = async (key, value, userId = null)=>{\n    try {\n        await delay();\n        // For now, just return success since this section doesn't have update endpoint yet\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('خطأ في تحديث إعدادات الشركة:', error);\n        throw error;\n    }\n};\n// ==================== Activity Log API ====================\nconst logActivity = async (userId, action, tableName, recordId, oldValues, newValues)=>{\n    try {\n        // For now, just log to console\n        console.log('Activity logged:', {\n            userId,\n            action,\n            tableName,\n            recordId,\n            oldValues,\n            newValues\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('خطأ في تسجيل النشاط:', error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./database/api-client.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%2Fnext%2Fdist%2Fpages%2F_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%2Fnext%2Fdist%2Fpages%2F_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/format-url */ \"next/dist/shared/lib/router/utils/format-url\");\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/request-meta */ \"./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/shared/lib/page-path/normalize-data-path */ \"next/dist/shared/lib/page-path/normalize-data-path\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/response-cache */ \"./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./node_modules/next/dist/pages/_error.js */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/shared/lib/utils */ \"next/dist/shared/lib/utils\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/lib/redirect-status */ \"./node_modules/next/dist/lib/redirect-status.js\");\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/lib/constants */ \"./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/server/send-payload */ \"./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/server/response-cache/utils */ \"./node_modules/next/dist/server/response-cache/utils.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/add-path-prefix */ \"next/dist/shared/lib/router/utils/add-path-prefix\");\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/remove-trailing-slash */ \"next/dist/shared/lib/router/utils/remove-trailing-slash\");\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__);\n\n\n\n\n\n\n\n\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    userland: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__\n});\nasync function handler(req, res, ctx) {\n    var _serverFilesManifest_config_experimental, _serverFilesManifest_config;\n    let srcPage = \"/_error\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return;\n    }\n    const { buildId, query, params, parsedUrl, originalQuery, originalPathname, buildManifest, nextFontManifest, serverFilesManifest, reactLoadableManifest, prerenderManifest, isDraftMode, isOnDemandRevalidate, revalidateOnlyGenerated, locale, locales, defaultLocale, routerServerContext, nextConfig, resolvedPathname } = prepareResult;\n    const isExperimentalCompile = serverFilesManifest == null ? void 0 : (_serverFilesManifest_config = serverFilesManifest.config) == null ? void 0 : (_serverFilesManifest_config_experimental = _serverFilesManifest_config.experimental) == null ? void 0 : _serverFilesManifest_config_experimental.isExperimentalCompile;\n    const hasServerProps = Boolean(getServerSideProps);\n    const hasStaticProps = Boolean(getStaticProps);\n    const hasStaticPaths = Boolean(getStaticPaths);\n    const hasGetInitialProps = Boolean(((_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13___default()) || _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__).getInitialProps);\n    const isAmp = query.amp && config.amp;\n    let cacheKey = null;\n    let isIsrFallback = false;\n    let isNextDataRequest = prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps);\n    const is404Page = srcPage === '/404';\n    const is500Page = srcPage === '/500';\n    const isErrorPage = srcPage === '/_error';\n    if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n        cacheKey = `${locale ? `/${locale}` : ''}${(srcPage === '/' || resolvedPathname === '/') && locale ? '' : resolvedPathname}${isAmp ? '.amp' : ''}`;\n        if (is404Page || is500Page || isErrorPage) {\n            cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`;\n        }\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    if (hasStaticPaths && !isDraftMode) {\n        const decodedPathname = (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(locale ? (0,next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__.addPathPrefix)(resolvedPathname, `/${locale}`) : resolvedPathname);\n        const isPrerendered = Boolean(prerenderManifest.routes[decodedPathname]) || prerenderManifest.notFoundRoutes.includes(decodedPathname);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__.NoFallbackError();\n            }\n            if (typeof prerenderInfo.fallback === 'string' && !isPrerendered && !isNextDataRequest) {\n                isIsrFallback = true;\n            }\n        }\n    }\n    // When serving a bot request, we want to serve a blocking render and not\n    // the prerendered page. This ensures that the correct content is served\n    // to the bot in the head.\n    if (isIsrFallback && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__.isBot)(req.headers['user-agent'] || '') || (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n        isIsrFallback = false;\n    }\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const method = req.method || 'GET';\n        const resolvedUrl = (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n            pathname: nextConfig.trailingSlash ? parsedUrl.pathname : (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(parsedUrl.pathname || '/'),\n            // make sure to only add query values from original URL\n            query: hasStaticProps ? {} : originalQuery\n        });\n        const publicRuntimeConfig = (routerServerContext == null ? void 0 : routerServerContext.publicRuntimeConfig) || nextConfig.publicRuntimeConfig;\n        const handleResponse = async (span)=>{\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                var _previousCacheEntry_value;\n                const doRender = async ()=>{\n                    try {\n                        var _nextConfig_i18n, _nextConfig_experimental_amp, _nextConfig_experimental_amp1;\n                        return await routeModule.render(req, res, {\n                            query: hasStaticProps && !isExperimentalCompile ? {\n                                ...params,\n                                ...isAmp ? {\n                                    amp: query.amp\n                                } : {}\n                            } : {\n                                ...query,\n                                ...params\n                            },\n                            params,\n                            page: srcPage,\n                            renderContext: {\n                                isDraftMode,\n                                isFallback: isIsrFallback,\n                                developmentNotFoundSourcePage: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'developmentNotFoundSourcePage')\n                            },\n                            sharedContext: {\n                                buildId,\n                                customServer: Boolean(routerServerContext == null ? void 0 : routerServerContext.isCustomServer) || undefined,\n                                deploymentId: false\n                            },\n                            renderOpts: {\n                                params,\n                                routeModule,\n                                page: srcPage,\n                                pageConfig: config || {},\n                                Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__),\n                                ComponentMod: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_13__,\n                                getStaticProps,\n                                getStaticPaths,\n                                getServerSideProps,\n                                supportsDynamicResponse: !hasStaticProps,\n                                buildManifest,\n                                nextFontManifest,\n                                reactLoadableManifest,\n                                assetPrefix: nextConfig.assetPrefix,\n                                strictNextHead: Boolean(nextConfig.experimental.strictNextHead),\n                                previewProps: prerenderManifest.preview,\n                                images: nextConfig.images,\n                                nextConfigOutput: nextConfig.output,\n                                optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                                nextScriptWorkers: Boolean(nextConfig.experimental.nextScriptWorkers),\n                                domainLocales: (_nextConfig_i18n = nextConfig.i18n) == null ? void 0 : _nextConfig_i18n.domains,\n                                crossOrigin: nextConfig.crossOrigin,\n                                multiZoneDraftMode,\n                                basePath: nextConfig.basePath,\n                                canonicalBase: nextConfig.amp.canonicalBase || '',\n                                ampOptimizerConfig: (_nextConfig_experimental_amp = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp.optimizer,\n                                disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n                                largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n                                // Only the `publicRuntimeConfig` key is exposed to the client side\n                                // It'll be rendered as part of __NEXT_DATA__ on the client side\n                                runtimeConfig: Object.keys(publicRuntimeConfig).length > 0 ? publicRuntimeConfig : undefined,\n                                isExperimentalCompile,\n                                experimental: {\n                                    clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                                },\n                                locale,\n                                locales,\n                                defaultLocale,\n                                setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                                isNextDataRequest: isNextDataRequest && (hasServerProps || hasStaticProps),\n                                resolvedUrl,\n                                // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                                // and not the resolved URL to prevent a hydration mismatch on\n                                // asPath\n                                resolvedAsPath: hasServerProps || hasGetInitialProps ? (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n                                    // we use the original URL pathname less the _next/data prefix if\n                                    // present\n                                    pathname: isNextDataRequest ? (0,next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__.normalizeDataPath)(originalPathname) : originalPathname,\n                                    query: originalQuery\n                                }) : resolvedUrl,\n                                isOnDemandRevalidate,\n                                ErrorDebug: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'PagesErrorDebug'),\n                                err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'invokeError'),\n                                dev: routeModule.isDev,\n                                // needed for experimental.optimizeCss feature\n                                distDir: `${routeModule.projectDir}/${routeModule.distDir}`,\n                                ampSkipValidation: (_nextConfig_experimental_amp1 = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp1.skipValidation,\n                                ampValidator: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'ampValidator')\n                            }\n                        }).then((renderResult)=>{\n                            const { metadata } = renderResult;\n                            let cacheControl = metadata.cacheControl;\n                            if ('isNotFound' in metadata && metadata.isNotFound) {\n                                return {\n                                    value: null,\n                                    cacheControl\n                                };\n                            }\n                            // Handle `isRedirect`.\n                            if (metadata.isRedirect) {\n                                return {\n                                    value: {\n                                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT,\n                                        props: metadata.pageData ?? metadata.flightData\n                                    },\n                                    cacheControl\n                                };\n                            }\n                            return {\n                                value: {\n                                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                                    html: renderResult,\n                                    pageData: renderResult.metadata.pageData,\n                                    headers: renderResult.metadata.headers,\n                                    status: renderResult.metadata.statusCode\n                                },\n                                cacheControl\n                            };\n                        }).finally(()=>{\n                            if (!span) return;\n                            span.setAttributes({\n                                'http.status_code': res.statusCode,\n                                'next.rsc': false\n                            });\n                            const rootSpanAttributes = tracer.getRootSpanAttributes();\n                            // We were unable to get attributes, probably OTEL is not enabled\n                            if (!rootSpanAttributes) {\n                                return;\n                            }\n                            if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest) {\n                                console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                                return;\n                            }\n                            const route = rootSpanAttributes.get('next.route');\n                            if (route) {\n                                const name = `${method} ${route}`;\n                                span.setAttributes({\n                                    'next.route': route,\n                                    'http.route': route,\n                                    'next.span_name': name\n                                });\n                                span.updateName(name);\n                            } else {\n                                span.updateName(`${method} ${req.url}`);\n                            }\n                        });\n                    } catch (err) {\n                        // if this is a background revalidate we need to report\n                        // the request error here as it won't be bubbled\n                        if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                            await routeModule.onRequestError(req, err, {\n                                routerKind: 'Pages Router',\n                                routePath: srcPage,\n                                routeType: 'render',\n                                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                                    isRevalidate: hasStaticProps,\n                                    isOnDemandRevalidate\n                                })\n                            }, routerServerContext);\n                        }\n                        throw err;\n                    }\n                };\n                // if we've already generated this page we no longer\n                // serve the fallback\n                if (previousCacheEntry) {\n                    isIsrFallback = false;\n                }\n                if (isIsrFallback) {\n                    const fallbackResponse = await routeModule.getResponseCache(req).get(routeModule.isDev ? null : locale ? `/${locale}${srcPage}` : srcPage, async ({ previousCacheEntry: previousFallbackCacheEntry = null })=>{\n                        if (!routeModule.isDev) {\n                            return (0,next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__.toResponseCacheEntry)(previousFallbackCacheEntry);\n                        }\n                        return doRender();\n                    }, {\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                        isFallback: true,\n                        isRoutePPREnabled: false,\n                        isOnDemandRevalidate: false,\n                        incrementalCache: await routeModule.getIncrementalCache(req, nextConfig, prerenderManifest),\n                        waitUntil: ctx.waitUntil\n                    });\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        fallbackResponse.isMiss = true;\n                        return fallbackResponse;\n                    }\n                }\n                if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                    res.statusCode = 404;\n                    // on-demand revalidate always sets this header\n                    res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                    res.end('This page could not be found');\n                    return null;\n                }\n                if (isIsrFallback && (previousCacheEntry == null ? void 0 : (_previousCacheEntry_value = previousCacheEntry.value) == null ? void 0 : _previousCacheEntry_value.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                    return {\n                        value: {\n                            kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                            html: new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(previousCacheEntry.value.html), {\n                                contentType: 'text/html;utf-8',\n                                metadata: {\n                                    statusCode: previousCacheEntry.value.status,\n                                    headers: previousCacheEntry.value.headers\n                                }\n                            }),\n                            pageData: {},\n                            status: previousCacheEntry.value.status,\n                            headers: previousCacheEntry.value.headers\n                        },\n                        cacheControl: {\n                            revalidate: 0,\n                            expire: undefined\n                        }\n                    };\n                }\n                return doRender();\n            };\n            const result = await routeModule.handleResponse({\n                cacheKey,\n                req,\n                nextConfig,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                waitUntil: ctx.waitUntil,\n                responseGenerator: responseGenerator,\n                prerenderManifest\n            });\n            // if we got a cache hit this wasn't an ISR fallback\n            // but it wasn't generated during build so isn't in the\n            // prerender-manifest\n            if (isIsrFallback && !(result == null ? void 0 : result.isMiss)) {\n                isIsrFallback = false;\n            }\n            // response is finished is no cache entry\n            if (!result) {\n                return;\n            }\n            if (hasStaticProps && !(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : result.isMiss ? 'MISS' : result.isStale ? 'STALE' : 'HIT');\n            }\n            let cacheControl;\n            if (!hasStaticProps || isIsrFallback) {\n                if (!res.getHeader('Cache-Control')) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                }\n            } else if (is404Page) {\n                const notFoundRevalidate = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'notFoundRevalidate');\n                cacheControl = {\n                    revalidate: typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n                    expire: undefined\n                };\n            } else if (is500Page) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (result.cacheControl) {\n                // If the cache entry has a cache control with a revalidate value that's\n                // a number, use it.\n                if (typeof result.cacheControl.revalidate === 'number') {\n                    var _result_cacheControl;\n                    if (result.cacheControl.revalidate < 1) {\n                        throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                            value: \"E22\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    cacheControl = {\n                        revalidate: result.cacheControl.revalidate,\n                        expire: ((_result_cacheControl = result.cacheControl) == null ? void 0 : _result_cacheControl.expire) ?? nextConfig.expireTime\n                    };\n                } else {\n                    // revalidate: false\n                    cacheControl = {\n                        revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__.CACHE_ONE_YEAR,\n                        expire: undefined\n                    };\n                }\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheControl && !res.getHeader('Cache-Control')) {\n                res.setHeader('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__.getCacheControlHeader)(cacheControl));\n            }\n            // notFound: true case\n            if (!result.value) {\n                var _result_cacheControl1;\n                // add revalidate metadata before rendering 404 page\n                // so that we can use this as source of truth for the\n                // cache-control header instead of what the 404 page returns\n                // for the revalidate value\n                (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.addRequestMeta)(req, 'notFoundRevalidate', (_result_cacheControl1 = result.cacheControl) == null ? void 0 : _result_cacheControl1.revalidate);\n                res.statusCode = 404;\n                if (isNextDataRequest) {\n                    res.end('{\"notFound\":true}');\n                    return;\n                }\n                // TODO: should route-module itself handle rendering the 404\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res, parsedUrl, false);\n                } else {\n                    res.end('This page could not be found');\n                }\n                return;\n            }\n            if (result.value.kind === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT) {\n                if (isNextDataRequest) {\n                    res.setHeader('content-type', 'application/json');\n                    res.end(JSON.stringify(result.value.props));\n                    return;\n                } else {\n                    const handleRedirect = (pageData)=>{\n                        const redirect = {\n                            destination: pageData.pageProps.__N_REDIRECT,\n                            statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n                            basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH\n                        };\n                        const statusCode = (0,next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__.getRedirectStatus)(redirect);\n                        const { basePath } = nextConfig;\n                        if (basePath && redirect.basePath !== false && redirect.destination.startsWith('/')) {\n                            redirect.destination = `${basePath}${redirect.destination}`;\n                        }\n                        if (redirect.destination.startsWith('/')) {\n                            redirect.destination = (0,next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__.normalizeRepeatedSlashes)(redirect.destination);\n                        }\n                        res.statusCode = statusCode;\n                        res.setHeader('Location', redirect.destination);\n                        if (statusCode === next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__.RedirectStatusCode.PermanentRedirect) {\n                            res.setHeader('Refresh', `0;url=${redirect.destination}`);\n                        }\n                        res.end(redirect.destination);\n                    };\n                    await handleRedirect(result.value.props);\n                    return null;\n                }\n            }\n            if (result.value.kind !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                throw Object.defineProperty(new Error(`Invariant: received non-pages cache entry in pages handler`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E695\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // when invoking _error before pages/500 we don't actually\n            // send the _error response\n            if ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'customErrorRender') || isErrorPage && (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && res.statusCode === 500) {\n                return null;\n            }\n            await (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__.sendRenderResult)({\n                req,\n                res,\n                // If we are rendering the error page it's not a data request\n                // anymore\n                result: isNextDataRequest && !isErrorPage && !is500Page ? new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(JSON.stringify(result.value.pageData)), {\n                    contentType: 'application/json',\n                    metadata: result.value.html.metadata\n                }) : result.value.html,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                cacheControl: routeModule.isDev ? undefined : cacheControl,\n                type: isNextDataRequest ? 'json' : 'html'\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse();\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        await routeModule.onRequestError(req, err, {\n            routerKind: 'Pages Router',\n            routePath: srcPage,\n            routeType: 'render',\n            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                isRevalidate: hasStaticProps,\n                isOnDemandRevalidate\n            })\n        }, routerServerContext);\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%2Fnext%2Fdist%2Fpages%2F_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_index_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/index.css */ \"./src/index.css\");\n/* harmony import */ var _src_index_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_index_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _src_admin_utils_initDatabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../src/admin/utils/initDatabase */ \"./src/admin/utils/initDatabase.js\");\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"App.useEffect\": ()=>{\n            // Set client-side flag\n            setIsClient(true);\n            // Initialize database on app start (client-side only)\n            if (false) {}\n        }\n    }[\"App.useEffect\"], []);\n    // Don't render anything until we're on the client side\n    if (!isClient) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"/var/www/html/pages/_app.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/var/www/html/pages/_app.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUF5QjtBQUNrQjtBQUN5QjtBQUVyRCxTQUFTRyxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFO0lBQ2xELE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHTiwrQ0FBUUEsQ0FBQztJQUV6Q0QsZ0RBQVNBO3lCQUFDO1lBQ1IsdUJBQXVCO1lBQ3ZCTyxZQUFZO1lBRVosc0RBQXNEO1lBQ3RELElBQUksS0FBNkIsRUFBRSxFQUVsQztRQUNIO3dCQUFHLEVBQUU7SUFFTCx1REFBdUQ7SUFDdkQsSUFBSSxDQUFDRCxVQUFVO1FBQ2IsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNLO1FBQUlDLEtBQUk7a0JBQ1AsNEVBQUNSO1lBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7QUFHOUIiLCJzb3VyY2VzIjpbIi92YXIvd3d3L2h0bWwvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3NyYy9pbmRleC5jc3MnXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBpbml0aWFsaXplRGF0YWJhc2UgfSBmcm9tICcuLi9zcmMvYWRtaW4vdXRpbHMvaW5pdERhdGFiYXNlJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9KSB7XG4gIGNvbnN0IFtpc0NsaWVudCwgc2V0SXNDbGllbnRdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBTZXQgY2xpZW50LXNpZGUgZmxhZ1xuICAgIHNldElzQ2xpZW50KHRydWUpXG5cbiAgICAvLyBJbml0aWFsaXplIGRhdGFiYXNlIG9uIGFwcCBzdGFydCAoY2xpZW50LXNpZGUgb25seSlcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGluaXRpYWxpemVEYXRhYmFzZSgpLmNhdGNoKGNvbnNvbGUuZXJyb3IpXG4gICAgfVxuICB9LCBbXSlcblxuICAvLyBEb24ndCByZW5kZXIgYW55dGhpbmcgdW50aWwgd2UncmUgb24gdGhlIGNsaWVudCBzaWRlXG4gIGlmICghaXNDbGllbnQpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGRpcj1cInJ0bFwiPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJpbml0aWFsaXplRGF0YWJhc2UiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJpc0NsaWVudCIsInNldElzQ2xpZW50IiwiY2F0Y2giLCJjb25zb2xlIiwiZXJyb3IiLCJkaXYiLCJkaXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        href: \"/icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.svg\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.svg\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"true\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"geo.region\",\n                        content: \"SA\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"geo.placename\",\n                        content: \"الرياض\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=yes\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://cdnjs.cloudflare.com\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/pages/_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/var/www/html/pages/_document.js\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/pages/_document.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/var/www/html/pages/_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTREO0FBRTdDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSztRQUFLQyxLQUFJOzswQkFDbEIsOERBQUNMLCtDQUFJQTs7a0NBRUgsOERBQUNNO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLO3dCQUFnQkMsTUFBSzs7Ozs7O2tDQUMzQyw4REFBQ0g7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7d0JBQWdCRSxPQUFNO3dCQUFRRCxNQUFLOzs7Ozs7a0NBQ3pELDhEQUFDSDt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzt3QkFBZ0JFLE9BQU07d0JBQVFELE1BQUs7Ozs7OztrQ0FDekQsOERBQUNIO3dCQUFLQyxLQUFJO3dCQUFtQkcsT0FBTTt3QkFBVUQsTUFBSzs7Ozs7O2tDQUNsRCw4REFBQ0g7d0JBQUtDLEtBQUk7d0JBQVdFLE1BQUs7Ozs7OztrQ0FHMUIsOERBQUNIO3dCQUFLQyxLQUFJO3dCQUFhRSxNQUFLOzs7Ozs7a0NBQzVCLDhEQUFDSDt3QkFBS0MsS0FBSTt3QkFBYUUsTUFBSzt3QkFBNEJFLGFBQVk7Ozs7OztrQ0FDcEUsOERBQUNMO3dCQUNDRyxNQUFLO3dCQUNMRixLQUFJOzs7Ozs7a0NBSU4sOERBQUNEO3dCQUNDRyxNQUFLO3dCQUNMRixLQUFJOzs7Ozs7a0NBSU4sOERBQUNLO3dCQUFLQyxNQUFLO3dCQUFhQyxTQUFROzs7Ozs7a0NBQ2hDLDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBZ0JDLFNBQVE7Ozs7OztrQ0FDbkMsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUFtQkMsU0FBUTs7Ozs7O2tDQUN0Qyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQXlCQyxTQUFROzs7Ozs7a0NBQzVDLDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBK0JDLFNBQVE7Ozs7OztrQ0FDbEQsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUFjQyxTQUFROzs7Ozs7a0NBR2pDLDhEQUFDUjt3QkFBS0MsS0FBSTt3QkFBYUUsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUU5Qiw4REFBQ007O2tDQUNDLDhEQUFDZCwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyIvdmFyL3d3dy9odG1sL3BhZ2VzL19kb2N1bWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSAnbmV4dC9kb2N1bWVudCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImFyXCIgZGlyPVwicnRsXCI+XG4gICAgICA8SGVhZD5cbiAgICAgICAgey8qIEZhdmljb24gYW5kIEljb25zICovfVxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgdHlwZT1cImltYWdlL3N2Zyt4bWxcIiBocmVmPVwiL2ljb24uc3ZnXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIHR5cGU9XCJpbWFnZS9zdmcreG1sXCIgc2l6ZXM9XCIxNngxNlwiIGhyZWY9XCIvZmF2aWNvbi0xNngxNi5zdmdcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgdHlwZT1cImltYWdlL3N2Zyt4bWxcIiBzaXplcz1cIjMyeDMyXCIgaHJlZj1cIi9mYXZpY29uLTMyeDMyLnN2Z1wiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImFwcGxlLXRvdWNoLWljb25cIiBzaXplcz1cIjE4MHgxODBcIiBocmVmPVwiL2FwcGxlLXRvdWNoLWljb24uc3ZnXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwibWFuaWZlc3RcIiBocmVmPVwiL3NpdGUud2VibWFuaWZlc3RcIiAvPlxuICAgICAgICBcbiAgICAgICAgey8qIEZvbnRzICovfVxuICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb21cIiAvPlxuICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vZm9udHMuZ3N0YXRpYy5jb21cIiBjcm9zc09yaWdpbj1cInRydWVcIiAvPlxuICAgICAgICA8bGluayBcbiAgICAgICAgICBocmVmPVwiaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbS9jc3MyP2ZhbWlseT1UYWphd2FsOndnaHRAMjAwOzMwMDs0MDA7NTAwOzcwMDs4MDA7OTAwJmRpc3BsYXk9c3dhcFwiIFxuICAgICAgICAgIHJlbD1cInN0eWxlc2hlZXRcIiBcbiAgICAgICAgLz5cbiAgICAgICAgXG4gICAgICAgIHsvKiBSZW1peCBJY29ucyAqL31cbiAgICAgICAgPGxpbmsgXG4gICAgICAgICAgaHJlZj1cImh0dHBzOi8vY2RuLmpzZGVsaXZyLm5ldC9ucG0vcmVtaXhpY29uQDQuMC4wL2ZvbnRzL3JlbWl4aWNvbi5jc3NcIiBcbiAgICAgICAgICByZWw9XCJzdHlsZXNoZWV0XCIgXG4gICAgICAgIC8+XG4gICAgICAgIFxuICAgICAgICB7LyogQWRkaXRpb25hbCBNZXRhIFRhZ3MgKi99XG4gICAgICAgIDxtZXRhIG5hbWU9XCJnZW8ucmVnaW9uXCIgY29udGVudD1cIlNBXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImdlby5wbGFjZW5hbWVcIiBjb250ZW50PVwi2KfZhNix2YrYp9i2XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImZvcm1hdC1kZXRlY3Rpb25cIiBjb250ZW50PVwidGVsZXBob25lPXllc1wiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJtb2JpbGUtd2ViLWFwcC1jYXBhYmxlXCIgY29udGVudD1cInllc1wiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJhcHBsZS1tb2JpbGUtd2ViLWFwcC1jYXBhYmxlXCIgY29udGVudD1cInllc1wiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ0aGVtZS1jb2xvclwiIGNvbnRlbnQ9XCIjM2I4MmY2XCIgLz5cbiAgICAgICAgXG4gICAgICAgIHsvKiBQcmVjb25uZWN0IGZvciBQZXJmb3JtYW5jZSAqL31cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2NkbmpzLmNsb3VkZmxhcmUuY29tXCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxib2R5PlxuICAgICAgICA8TWFpbiAvPlxuICAgICAgICA8TmV4dFNjcmlwdCAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvSHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJkaXIiLCJsaW5rIiwicmVsIiwidHlwZSIsImhyZWYiLCJzaXplcyIsImNyb3NzT3JpZ2luIiwibWV0YSIsIm5hbWUiLCJjb250ZW50IiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_document.js\n");

/***/ }),

/***/ "./src/admin/utils/initDatabase.js":
/*!*****************************************!*\
  !*** ./src/admin/utils/initDatabase.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   forceResetDatabase: () => (/* binding */ forceResetDatabase),\n/* harmony export */   getDatabaseStatus: () => (/* binding */ getDatabaseStatus),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   isDatabaseInitialized: () => (/* binding */ isDatabaseInitialized),\n/* harmony export */   resetDatabase: () => (/* binding */ resetDatabase)\n/* harmony export */ });\n/* harmony import */ var _database_api_client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../database/api-client.js */ \"./database/api-client.js\");\n// Database initialization utility\n// أداة تهيئة قاعدة البيانات\n// This file ensures that the database is properly initialized\n// when the admin panel starts up\n// لا نحتاج إلى initDatabase من database.js لأننا نستخدم API backend الآن\n\n// Initialize all database collections with default data\nconst initializeDatabase = async ()=>{\n    try {\n        console.log('🔄 Initializing database connection...');\n        // Test API connection by calling get functions\n        // This will verify that the API backend is running\n        await Promise.all([\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getHeroData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getWhyChooseUsData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getKitchensData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getCabinetsData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getFooterData)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getCategories)(),\n            (0,_database_api_client_js__WEBPACK_IMPORTED_MODULE_0__.getCompanySettings)()\n        ]);\n        console.log('✅ Database connection initialized successfully');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to initialize database connection:', error);\n        console.error('تأكد من أن API server يعمل على http://localhost:3002');\n        return false;\n    }\n};\n// Check if database has been initialized\nconst isDatabaseInitialized = ()=>{\n    try {\n        // Check if basic data exists in localStorage\n        const heroData = localStorage.getItem('db_hero_section');\n        const categoriesData = localStorage.getItem('db_categories');\n        return !!(heroData && categoriesData);\n    } catch (error) {\n        console.error('Error checking database initialization:', error);\n        return false;\n    }\n};\n// Reset database to default state\nconst resetDatabase = ()=>{\n    try {\n        console.log('🔄 Resetting database...');\n        // Clear all database storage\n        const dbKeys = [\n            'db_hero_section',\n            'db_why_choose_us',\n            'db_kitchens',\n            'db_cabinets',\n            'db_footer',\n            'db_categories',\n            'db_company_settings',\n            'db_activity_log'\n        ];\n        dbKeys.forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n        console.log('✅ Database reset successfully');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to reset database:', error);\n        return false;\n    }\n};\n// Export database status\nconst getDatabaseStatus = ()=>{\n    try {\n        const status = {\n            initialized: isDatabaseInitialized(),\n            collections: {}\n        };\n        // Check each collection\n        const collections = [\n            'db_hero_section',\n            'db_why_choose_us',\n            'db_kitchens',\n            'db_cabinets',\n            'db_footer',\n            'db_categories',\n            'db_company_settings'\n        ];\n        collections.forEach((collection)=>{\n            const data = localStorage.getItem(collection);\n            status.collections[collection] = {\n                exists: !!data,\n                size: data ? data.length : 0\n            };\n        });\n        return status;\n    } catch (error) {\n        console.error('Error getting database status:', error);\n        return {\n            initialized: false,\n            collections: {},\n            error: error.message\n        };\n    }\n};\n// Force reset database and recreate with new data\nconst forceResetDatabase = async ()=>{\n    try {\n        console.log('🔄 Force resetting database...');\n        // For SQLite backend, we would need to call a reset API endpoint\n        // For now, just reinitialize the connection\n        await initializeDatabase();\n        console.log('✅ Database connection reset completed');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to force reset database:', error);\n        return false;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    initializeDatabase,\n    isDatabaseInitialized,\n    resetDatabase,\n    getDatabaseStatus,\n    forceResetDatabase\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/admin/utils/initDatabase.js\n");

/***/ }),

/***/ "./src/config/api.js":
/*!***************************!*\
  !*** ./src/config/api.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   getApiURL: () => (/* binding */ getApiURL),\n/* harmony export */   getImageURL: () => (/* binding */ getImageURL)\n/* harmony export */ });\n// API Configuration\n// إعدادات API للبيئات المختلفة\nconst getApiConfig = ()=>{\n    // Check if we're in browser environment\n    if (true) {\n        // Server-side rendering - return default config\n        return {\n            baseURL: '/api',\n            uploadsURL: ''\n        };\n    }\n    // تحديد البيئة الحالية\n    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';\n    if (isDevelopment) {\n        return {\n            baseURL: 'http://localhost:3002',\n            uploadsURL: '' // روابط نسبية - الصور في public/uploads\n        };\n    } else {\n        // للإنتاج وجميع البيئات الأخرى\n        return {\n            baseURL: `${window.location.protocol}//${window.location.host}/api`,\n            uploadsURL: '' // روابط نسبية - الصور في public/uploads\n        };\n    }\n};\nconst API_CONFIG = getApiConfig();\n// دالة مساعدة لبناء URL كامل للصور\nconst getImageURL = (imagePath)=>{\n    if (!imagePath) return null;\n    // إذا كان الرابط كاملاً بالفعل، أرجعه كما هو\n    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n        return imagePath;\n    }\n    // الصور الآن في public/uploads، لذا نستخدم روابط نسبية مباشرة\n    // هذا يعمل مع HTTP و HTTPS تلقائياً\n    return imagePath;\n};\n// دالة مساعدة لبناء URL للـ API\nconst getApiURL = (endpoint)=>{\n    return `${API_CONFIG.baseURL}${endpoint}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/api.js\n");

/***/ }),

/***/ "./src/index.css":
/*!***********************!*\
  !*** ./src/index.css ***!
  \***********************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/page-path/normalize-data-path":
/*!*********************************************************************!*\
  !*** external "next/dist/shared/lib/page-path/normalize-data-path" ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/page-path/normalize-data-path");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/add-path-prefix":
/*!********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/add-path-prefix" ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/format-url":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/format-url" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-url");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/remove-trailing-slash":
/*!**************************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/remove-trailing-slash" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash");

/***/ }),

/***/ "next/dist/shared/lib/utils":
/*!*********************************************!*\
  !*** external "next/dist/shared/lib/utils" ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("./webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%2Fnext%2Fdist%2Fpages%2F_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();