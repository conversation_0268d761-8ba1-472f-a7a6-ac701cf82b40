[{"/var/www/html/pages/_app.js": "1", "/var/www/html/pages/_document.js": "2", "/var/www/html/pages/admin.js": "3", "/var/www/html/pages/cabinets.js": "4", "/var/www/html/pages/index.js": "5", "/var/www/html/pages/kitchens.js": "6", "/var/www/html/src/admin/components/Dashboard.jsx": "7", "/var/www/html/src/admin/components/Header.jsx": "8", "/var/www/html/src/admin/components/Login.jsx": "9", "/var/www/html/src/admin/components/Sidebar.jsx": "10", "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx": "11", "/var/www/html/src/admin/components/sections/DashboardHome.jsx": "12", "/var/www/html/src/admin/components/sections/FooterManagement.jsx": "13", "/var/www/html/src/admin/components/sections/HeroManagement.jsx": "14", "/var/www/html/src/admin/components/sections/KitchensManagement.jsx": "15", "/var/www/html/src/admin/components/sections/UserManagement.jsx": "16", "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx": "17", "/var/www/html/src/admin/context/AuthContext.jsx": "18", "/var/www/html/src/admin/context/DataContext.jsx": "19", "/var/www/html/src/admin/utils/initDatabase.js": "20", "/var/www/html/src/components/CabinetGallery.jsx": "21", "/var/www/html/src/components/CallToAction.jsx": "22", "/var/www/html/src/components/Footer.jsx": "23", "/var/www/html/src/components/HeroSection.jsx": "24", "/var/www/html/src/components/KitchenGallery.jsx": "25", "/var/www/html/src/components/Navbar.jsx": "26", "/var/www/html/src/components/SEO.jsx": "27", "/var/www/html/src/components/Testimonials.jsx": "28", "/var/www/html/src/components/WhyChooseUs.jsx": "29", "/var/www/html/src/config/api.js": "30", "/var/www/html/src/config/env.js": "31", "/var/www/html/src/pages/AdminPage.jsx": "32", "/var/www/html/src/pages/CabinetsPage.jsx": "33", "/var/www/html/src/pages/HomePage.jsx": "34", "/var/www/html/src/pages/KitchensPage.jsx": "35"}, {"size": 663, "mtime": 1752632417301, "results": "36", "hashOfConfig": "37"}, {"size": 1725, "mtime": 1752631575132, "results": "38", "hashOfConfig": "37"}, {"size": 3441, "mtime": 1752631724686, "results": "39", "hashOfConfig": "37"}, {"size": 6368, "mtime": 1752631689240, "results": "40", "hashOfConfig": "37"}, {"size": 2347, "mtime": 1752631603324, "results": "41", "hashOfConfig": "37"}, {"size": 6684, "mtime": 1752631654636, "results": "42", "hashOfConfig": "37"}, {"size": 3566, "mtime": 1751444435516, "results": "43", "hashOfConfig": "37"}, {"size": 5952, "mtime": 1751443429426, "results": "44", "hashOfConfig": "37"}, {"size": 9709, "mtime": 1751561588128, "results": "45", "hashOfConfig": "37"}, {"size": 10324, "mtime": 1751443430306, "results": "46", "hashOfConfig": "37"}, {"size": 18409, "mtime": 1751686925506, "results": "47", "hashOfConfig": "37"}, {"size": 9439, "mtime": 1751596455083, "results": "48", "hashOfConfig": "37"}, {"size": 17788, "mtime": 1751601289625, "results": "49", "hashOfConfig": "37"}, {"size": 11914, "mtime": 1751667340164, "results": "50", "hashOfConfig": "37"}, {"size": 17940, "mtime": 1751686839673, "results": "51", "hashOfConfig": "37"}, {"size": 14750, "mtime": 1751443432969, "results": "52", "hashOfConfig": "37"}, {"size": 12975, "mtime": 1751601688079, "results": "53", "hashOfConfig": "37"}, {"size": 3828, "mtime": 1751478236235, "results": "54", "hashOfConfig": "37"}, {"size": 10376, "mtime": 1751606566385, "results": "55", "hashOfConfig": "37"}, {"size": 3715, "mtime": 1751682164034, "results": "56", "hashOfConfig": "37"}, {"size": 32342, "mtime": 1752632174918, "results": "57", "hashOfConfig": "37"}, {"size": 923, "mtime": 1751443427609, "results": "58", "hashOfConfig": "37"}, {"size": 4853, "mtime": 1751693886054, "results": "59", "hashOfConfig": "37"}, {"size": 2901, "mtime": 1751667200221, "results": "60", "hashOfConfig": "37"}, {"size": 35082, "mtime": 1752632275538, "results": "61", "hashOfConfig": "37"}, {"size": 16864, "mtime": 1752631886012, "results": "62", "hashOfConfig": "37"}, {"size": 5001, "mtime": 1752632019825, "results": "63", "hashOfConfig": "37"}, {"size": 3517, "mtime": 1751443429007, "results": "64", "hashOfConfig": "37"}, {"size": 9400, "mtime": 1751696106894, "results": "65", "hashOfConfig": "37"}, {"size": 1569, "mtime": 1752632482411, "results": "66", "hashOfConfig": "37"}, {"size": 5079, "mtime": 1752632642474, "results": "67", "hashOfConfig": "37"}, {"size": 3204, "mtime": 1751696208539, "results": "68", "hashOfConfig": "37"}, {"size": 28475, "mtime": 1751853906207, "results": "69", "hashOfConfig": "37"}, {"size": 1651, "mtime": 1751696359377, "results": "70", "hashOfConfig": "37"}, {"size": 28606, "mtime": 1751853861313, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lomtko", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/var/www/html/pages/_app.js", [], [], "/var/www/html/pages/_document.js", [], [], "/var/www/html/pages/admin.js", [], [], "/var/www/html/pages/cabinets.js", ["177", "178"], [], "/var/www/html/pages/index.js", [], [], "/var/www/html/pages/kitchens.js", ["179", "180"], [], "/var/www/html/src/admin/components/Dashboard.jsx", [], [], "/var/www/html/src/admin/components/Header.jsx", [], [], "/var/www/html/src/admin/components/Login.jsx", [], [], "/var/www/html/src/admin/components/Sidebar.jsx", [], [], "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx", ["181", "182"], [], "/var/www/html/src/admin/components/sections/DashboardHome.jsx", [], [], "/var/www/html/src/admin/components/sections/FooterManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/HeroManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/KitchensManagement.jsx", ["183", "184"], [], "/var/www/html/src/admin/components/sections/UserManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx", [], [], "/var/www/html/src/admin/context/AuthContext.jsx", [], [], "/var/www/html/src/admin/context/DataContext.jsx", [], [], "/var/www/html/src/admin/utils/initDatabase.js", ["185"], [], "/var/www/html/src/components/CabinetGallery.jsx", ["186", "187", "188", "189", "190"], [], "/var/www/html/src/components/CallToAction.jsx", [], [], "/var/www/html/src/components/Footer.jsx", [], [], "/var/www/html/src/components/HeroSection.jsx", [], [], "/var/www/html/src/components/KitchenGallery.jsx", ["191", "192", "193", "194", "195"], [], "/var/www/html/src/components/Navbar.jsx", [], [], "/var/www/html/src/components/SEO.jsx", [], [], "/var/www/html/src/components/Testimonials.jsx", [], [], "/var/www/html/src/components/WhyChooseUs.jsx", ["196"], [], "/var/www/html/src/config/api.js", [], [], "/var/www/html/src/config/env.js", [], [], "/var/www/html/src/pages/AdminPage.jsx", [], [], "/var/www/html/src/pages/CabinetsPage.jsx", ["197", "198", "199", "200", "201"], [], "/var/www/html/src/pages/HomePage.jsx", [], [], "/var/www/html/src/pages/KitchensPage.jsx", ["202", "203", "204", "205", "206"], [], {"ruleId": "207", "severity": 1, "message": "208", "line": 74, "column": 6, "nodeType": "209", "endLine": 74, "endColumn": 8, "suggestions": "210"}, {"ruleId": "211", "severity": 1, "message": "212", "line": 119, "column": 21, "nodeType": "213", "endLine": 123, "endColumn": 23}, {"ruleId": "207", "severity": 1, "message": "214", "line": 78, "column": 6, "nodeType": "209", "endLine": 78, "endColumn": 8, "suggestions": "215"}, {"ruleId": "211", "severity": 1, "message": "212", "line": 123, "column": 21, "nodeType": "213", "endLine": 127, "endColumn": 23}, {"ruleId": "211", "severity": 1, "message": "212", "line": 285, "column": 15, "nodeType": "213", "endLine": 289, "endColumn": 17}, {"ruleId": "211", "severity": 1, "message": "212", "line": 318, "column": 23, "nodeType": "213", "endLine": 318, "endColumn": 101}, {"ruleId": "211", "severity": 1, "message": "212", "line": 277, "column": 15, "nodeType": "213", "endLine": 281, "endColumn": 17}, {"ruleId": "211", "severity": 1, "message": "212", "line": 310, "column": 23, "nodeType": "213", "endLine": 310, "endColumn": 101}, {"ruleId": "216", "severity": 1, "message": "217", "line": 138, "column": 1, "nodeType": "218", "endLine": 144, "endColumn": 2}, {"ruleId": "211", "severity": 1, "message": "212", "line": 275, "column": 23, "nodeType": "213", "endLine": 279, "endColumn": 25}, {"ruleId": "211", "severity": 1, "message": "212", "line": 389, "column": 25, "nodeType": "213", "endLine": 393, "endColumn": 27}, {"ruleId": "211", "severity": 1, "message": "212", "line": 493, "column": 29, "nodeType": "213", "endLine": 497, "endColumn": 31}, {"ruleId": "211", "severity": 1, "message": "212", "line": 531, "column": 17, "nodeType": "213", "endLine": 535, "endColumn": 19}, {"ruleId": "211", "severity": 1, "message": "212", "line": 626, "column": 27, "nodeType": "213", "endLine": 630, "endColumn": 29}, {"ruleId": "211", "severity": 1, "message": "212", "line": 396, "column": 19, "nodeType": "213", "endLine": 400, "endColumn": 21}, {"ruleId": "211", "severity": 1, "message": "212", "line": 468, "column": 25, "nodeType": "213", "endLine": 472, "endColumn": 27}, {"ruleId": "211", "severity": 1, "message": "212", "line": 572, "column": 29, "nodeType": "213", "endLine": 576, "endColumn": 31}, {"ruleId": "211", "severity": 1, "message": "212", "line": 610, "column": 17, "nodeType": "213", "endLine": 614, "endColumn": 19}, {"ruleId": "211", "severity": 1, "message": "212", "line": 705, "column": 27, "nodeType": "213", "endLine": 709, "endColumn": 29}, {"ruleId": "207", "severity": 1, "message": "219", "line": 99, "column": 6, "nodeType": "209", "endLine": 99, "endColumn": 8, "suggestions": "220"}, {"ruleId": "211", "severity": 1, "message": "212", "line": 246, "column": 21, "nodeType": "213", "endLine": 250, "endColumn": 23}, {"ruleId": "211", "severity": 1, "message": "212", "line": 358, "column": 25, "nodeType": "213", "endLine": 362, "endColumn": 27}, {"ruleId": "211", "severity": 1, "message": "212", "line": 448, "column": 29, "nodeType": "213", "endLine": 452, "endColumn": 31}, {"ruleId": "211", "severity": 1, "message": "212", "line": 486, "column": 17, "nodeType": "213", "endLine": 490, "endColumn": 19}, {"ruleId": "211", "severity": 1, "message": "212", "line": 567, "column": 27, "nodeType": "213", "endLine": 571, "endColumn": 29}, {"ruleId": "211", "severity": 1, "message": "212", "line": 249, "column": 21, "nodeType": "213", "endLine": 253, "endColumn": 23}, {"ruleId": "211", "severity": 1, "message": "212", "line": 361, "column": 25, "nodeType": "213", "endLine": 365, "endColumn": 27}, {"ruleId": "211", "severity": 1, "message": "212", "line": 451, "column": 29, "nodeType": "213", "endLine": 455, "endColumn": 31}, {"ruleId": "211", "severity": 1, "message": "212", "line": 489, "column": 17, "nodeType": "213", "endLine": 493, "endColumn": 19}, {"ruleId": "211", "severity": 1, "message": "212", "line": 570, "column": 27, "nodeType": "213", "endLine": 574, "endColumn": 29}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fallbackCabinets'. Either include it or remove the dependency array.", "ArrayExpression", ["221"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fallbackKitchens'. Either include it or remove the dependency array.", ["222"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'defaultWhyChooseUsData'. Either include it or remove the dependency array.", ["223"], {"desc": "224", "fix": "225"}, {"desc": "226", "fix": "227"}, {"desc": "228", "fix": "229"}, "Update the dependencies array to be: [fallbackCabinets]", {"range": "230", "text": "231"}, "Update the dependencies array to be: [fallback<PERSON><PERSON><PERSON>]", {"range": "232", "text": "233"}, "Update the dependencies array to be: [defaultWhyChooseUsData]", {"range": "234", "text": "235"}, [2866, 2868], "[fallbackCabinets]", [3099, 3101], "[fallback<PERSON><PERSON><PERSON>]", [3175, 3177], "[defaultWhyChooseUsData]"]