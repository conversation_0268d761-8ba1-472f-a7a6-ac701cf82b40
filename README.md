# خبرة المطابخ - Khobra Kitchens

موقع شركة خبرة المطابخ المتخصصة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية بأعلى معايير الجودة في المملكة العربية السعودية.

## 🌟 المميزات

- **تصميم متجاوب**: يعمل على جميع الأجهزة والشاشات
- **لوحة تحكم متقدمة**: إدارة شاملة للمحتوى
- **تحسين محركات البحث**: SEO محسن للسوق السعودي
- **أمان عالي**: حماية متقدمة للبيانات
- **أداء سريع**: تحميل سريع وتجربة مستخدم ممتازة

## 🚀 التقنيات المستخدمة

- **Frontend**: React 19, Vite, Tailwind CSS
- **Routing**: React Router DOM
- **Animations**: Framer Motion
- **Carousel**: Swiper.js
- **SEO**: React Helmet Async
- **Icons**: Remix Icons
- **Fonts**: Google Fonts (Tajawal)

## 📋 المتطلبات

- Node.js 18+
- npm أو yarn
- Git

## 🛠️ التثبيت والتشغيل

### 1. استنساخ المشروع

```bash
git clone https://github.com/your-repo/khobra-kitchens.git
cd khobra-kitchens
```

### 2. تثبيت التبعيات

```bash
npm install --legacy-peer-deps
```

### 3. إعداد متغيرات البيئة

```bash
cp .env.example .env
# قم بتحديث القيم في ملف .env
```

### 4. تشغيل المشروع

```bash
# للتطوير
npm run dev

# للبناء
npm run build

# للمعاينة
npm run preview
```

## 🔐 لوحة التحكم

### الوصول للوحة التحكم:
- **الرابط**: `/admin`
- **اسم المستخدم**: `admin_khobra_kitchens`
- **كلمة المرور**: `khobra_admin_2024`

### أقسام لوحة التحكم:
- **الرئيسية**: نظرة عامة على النظام
- **إدارة الهيرو**: تحرير القسم الرئيسي
- **لماذا تختارنا**: إدارة المميزات
- **إدارة المطابخ**: معرض المطابخ
- **إدارة الخزائن**: معرض الخزائن
- **إدارة الفوتر**: روابط التواصل
- **إدارة المستخدمين**: بيانات المستخدم

## 🌐 النشر

### النشر السريع:
```bash
chmod +x deploy.sh
./deploy.sh
```

### النشر باستخدام Docker:
```bash
docker-compose up -d
```

للمزيد من التفاصيل، راجع [دليل النشر](DEPLOYMENT_GUIDE.md)

## 📞 التواصل

- **الموقع**: https://khobrakitchens.com
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +************
- **العنوان**: الرياض، المملكة العربية السعودية

---

**© 2024 خبرة المطابخ. جميع الحقوق محفوظة.**
