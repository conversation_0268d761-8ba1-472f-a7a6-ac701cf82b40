#!/bin/bash

# PM2 Management Script for Khobra Kitchens
# سكريبت إدارة PM2 لخبرة المطابخ

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    echo "استخدام: $0 [الأمر]"
    echo ""
    echo "الأوامر المتاحة:"
    echo "  start       - تشغيل جميع الخدمات"
    echo "  stop        - إيقاف جميع الخدمات"
    echo "  restart     - إعادة تشغيل جميع الخدمات"
    echo "  status      - عرض حالة الخدمات"
    echo "  logs        - عرض السجلات"
    echo "  logs-api    - عرض سجلات API فقط"
    echo "  logs-web    - عرض سجلات الواجهة فقط"
    echo "  reload      - إعادة تحميل الإعدادات"
    echo "  save        - حفظ الحالة الحالية"
    echo "  monit       - مراقبة الأداء"
    echo "  help        - عرض هذه المساعدة"
}

start_services() {
    print_status "تشغيل خدمات خبرة المطابخ..."
    pm2 start ecosystem.config.cjs --env production
    if [ $? -eq 0 ]; then
        print_success "تم تشغيل جميع الخدمات بنجاح"
    else
        print_error "فشل في تشغيل الخدمات"
        exit 1
    fi
}

stop_services() {
    print_status "إيقاف خدمات خبرة المطابخ..."
    pm2 stop all
    if [ $? -eq 0 ]; then
        print_success "تم إيقاف جميع الخدمات بنجاح"
    else
        print_error "فشل في إيقاف الخدمات"
        exit 1
    fi
}

restart_services() {
    print_status "إعادة تشغيل خدمات خبرة المطابخ..."
    pm2 restart all
    if [ $? -eq 0 ]; then
        print_success "تم إعادة تشغيل جميع الخدمات بنجاح"
    else
        print_error "فشل في إعادة تشغيل الخدمات"
        exit 1
    fi
}

show_status() {
    print_status "حالة خدمات خبرة المطابخ:"
    pm2 list
}

show_logs() {
    print_status "عرض سجلات جميع الخدمات:"
    pm2 logs
}

show_api_logs() {
    print_status "عرض سجلات API:"
    pm2 logs khobra-api
}

show_web_logs() {
    print_status "عرض سجلات الواجهة:"
    pm2 logs khobra-frontend
}

reload_services() {
    print_status "إعادة تحميل إعدادات خدمات خبرة المطابخ..."
    pm2 reload ecosystem.config.cjs --env production
    if [ $? -eq 0 ]; then
        print_success "تم إعادة تحميل الإعدادات بنجاح"
    else
        print_error "فشل في إعادة تحميل الإعدادات"
        exit 1
    fi
}

save_state() {
    print_status "حفظ حالة PM2..."
    pm2 save
    if [ $? -eq 0 ]; then
        print_success "تم حفظ الحالة بنجاح"
    else
        print_error "فشل في حفظ الحالة"
        exit 1
    fi
}

monitor_services() {
    print_status "مراقبة أداء الخدمات:"
    pm2 monit
}

# Main script logic
case "$1" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    logs-api)
        show_api_logs
        ;;
    logs-web)
        show_web_logs
        ;;
    reload)
        reload_services
        ;;
    save)
        save_state
        ;;
    monit)
        monitor_services
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "أمر غير صحيح: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
