# إعداد Unsplash API للمطابخ والخزانات
# Unsplash API Setup for Kitchens and Cabinets

## الحصول على مفاتيح Unsplash API

### الخطوة 1: إنشاء حساب
1. اذهب إلى [Unsplash.com](https://unsplash.com/)
2. اض<PERSON><PERSON> على "Join" أو "إنشاء حساب"
3. أدخل بياناتك وأكمل التسجيل

### الخطوة 2: إنشاء تطبيق جديد
1. بعد تسجيل الدخول، اذهب إلى [Unsplash Developers](https://unsplash.com/developers)
2. اضغط على "Your apps" ثم "New Application"
3. اقبل الشروط والأحكام
4. أدخل اسم التطبيق ووصفه

### الخطوة 3: الحصول على المفاتيح
1. بعد إنشاء التطبيق، ستحصل على:
   - **Application ID**
   - **Access Key**
   - **Secret Key**
2. انسخ هذه المفاتيح الثلاثة

### الخطوة 4: إضافة المفاتيح للمشروع
1. افتح ملف `.env` في جذر المشروع
2. استبدل القيم التجريبية بمفاتيحك الحقيقية:
```env
VITE_UNSPLASH_APPLICATION_ID="your_actual_application_id"
VITE_UNSPLASH_ACCESS_KEY="your_actual_access_key"
VITE_UNSPLASH_SECRET_KEY="your_actual_secret_key"
```

## الصفحات المضافة

### 1. صفحة المطابخ (`/kitchens`)
- **الرابط**: `https://khobrakitchens.com/kitchens`
- **الفئات المتاحة**:
  - المطابخ عامة
  - المطابخ العصرية
  - المطابخ الكلاسيكية
  - المطابخ الفاخرة
  - المطابخ الإسكندنافية
  - المطابخ المينيمال

### 2. صفحة الخزانات (`/cabinets`)
- **الرابط**: `https://khobrakitchens.com/cabinets`
- **الفئات المتاحة**:
  - الخزانات عامة
  - خزانات الملابس
  - الخزانات العصرية
  - الخزانات الكلاسيكية
  - الخزانات الفاخرة
  - الخزانات المدمجة

## الميزات المضافة

### 1. عرض المنتجات
- **بطاقات مميزة** لكل منتج
- **صور عالية الجودة** من Pixabay
- **معلومات المنتج** (العنوان، الوصف، المشاهدات، الإعجابات)
- **تصفية حسب الفئة**

### 2. المودال التفاعلي
- **عرض الصور بحجم كبير**
- **تصفح الصور** إذا كان هناك أكثر من صورة
- **أزرار التواصل**:
  - واتساب
  - الهاتف
  - الإيميل
- **روابط وسائل التواصل الاجتماعي**

### 3. التنقل المحسن
- **روابط جديدة في النافبار**
- **تمييز الصفحة النشطة**
- **تنقل سلس بين الصفحات**

## الكلمات المفتاحية المستخدمة

### للمطابخ:
```javascript
const kitchenKeywords = {
  'kitchen': 'modern kitchen',
  'modern': 'modern kitchen design',
  'classic': 'classic kitchen interior',
  'luxury': 'luxury kitchen design',
  'scandinavian': 'scandinavian kitchen',
  'minimal': 'minimalist kitchen'
}
```

### للخزانات:
```javascript
const cabinetKeywords = {
  'cabinet': 'wardrobe closet',
  'wardrobe': 'bedroom wardrobe',
  'modern-cabinet': 'modern wardrobe',
  'classic-cabinet': 'classic wardrobe',
  'luxury-cabinet': 'luxury closet',
  'built-in': 'built-in wardrobe'
}
```

## اختبار النظام

### 1. اختبار محلي
```bash
# تشغيل الخادم المحلي
npm run dev

# زيارة الصفحات
http://localhost:5173/kitchens
http://localhost:5173/cabinets
```

### 2. اختبار على الموقع الحقيقي
```bash
# بناء المشروع
npm run build

# إعادة تشغيل الخدمات
pm2 restart khobra-frontend

# زيارة الصفحات
https://khobrakitchens.com/kitchens
https://khobrakitchens.com/cabinets
```

### 3. اختبار Unsplash API مباشرة
```bash
curl -H "Authorization: Client-ID YOUR_ACCESS_KEY" "https://api.unsplash.com/search/photos?query=modern%20kitchen&per_page=3"
```

## استكشاف الأخطاء

### إذا لم تظهر الصور:
1. تأكد من صحة مفتاح Pixabay API
2. تحقق من اتصال الإنترنت
3. افتح Developer Tools وتحقق من Console للأخطاء

### إذا لم تعمل الروابط:
1. تأكد من بناء المشروع: `npm run build`
2. أعد تشغيل الخدمات: `pm2 restart all`
3. تحقق من nginx configuration

### إذا لم يعمل المودال:
1. تأكد من وجود ملف `ProductModal.jsx`
2. تحقق من إعدادات CSS في `index.css`
3. تأكد من وجود أيقونات Remix Icon

## الملفات المضافة/المعدلة

### ملفات جديدة:
- `src/pages/KitchensPage.jsx`
- `src/pages/CabinetsPage.jsx`
- `src/components/ProductModal.jsx`
- `PIXABAY_SETUP.md`

### ملفات معدلة:
- `src/App.jsx` - إضافة الروابط الجديدة
- `src/components/Navbar.jsx` - تحديث النافبار
- `src/index.css` - إضافة CSS للـ spinner والـ line-clamp
- `.env` - إضافة مفتاح Pixabay API
- `.env.example` - إضافة مثال للمفتاح

---

**ملاحظة**: تأكد من الحصول على مفتاح Pixabay API حقيقي لضمان عمل النظام بشكل صحيح.
